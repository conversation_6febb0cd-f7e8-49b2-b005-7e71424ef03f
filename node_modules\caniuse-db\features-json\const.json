{"title": "const", "description": "Declares a constant with block level scope", "spec": "https://www.ecma-international.org/ecma-262/6.0/#sec-let-and-const-declarations", "status": "other", "links": [{"url": "http://generatedcontent.org/post/54444832868/variables-and-constants-in-es6", "title": "Variables and Constants in ES6"}, {"url": "https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/const", "title": "MDN Web Docs - const"}], "bugs": [], "categories": ["JS"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "y"}, "edge": {"12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y"}, "firefox": {"2": "a #1", "3": "a #1", "3.5": "a #1", "3.6": "a #1", "4": "a #1", "5": "a #1", "6": "a #1", "7": "a #1", "8": "a #1", "9": "a #1", "10": "a #1", "11": "a #1", "12": "a #1", "13": "a #2", "14": "a #2", "15": "a #2", "16": "a #2", "17": "a #2", "18": "a #2", "19": "a #2", "20": "a #2", "21": "a #2", "22": "a #2", "23": "a #2", "24": "a #2", "25": "a #2", "26": "a #2", "27": "a #2", "28": "a #2", "29": "a #2", "30": "a #2", "31": "a #2", "32": "a #2", "33": "a #2", "34": "a #2", "35": "a #2", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y"}, "chrome": {"4": "a #2", "5": "a #2", "6": "a #2", "7": "a #2", "8": "a #2", "9": "a #2", "10": "a #2", "11": "a #2", "12": "a #2", "13": "a #2", "14": "a #2", "15": "a #2", "16": "a #2", "17": "a #2", "18": "a #2", "19": "a #2", "20": "a #2", "21": "a #2 #3", "22": "a #2 #3", "23": "a #2 #3", "24": "a #2 #3", "25": "a #2 #3", "26": "a #2 #3", "27": "a #2 #3", "28": "a #2 #3", "29": "a #2 #3", "30": "a #2 #3", "31": "a #2 #3", "32": "a #2 #3", "33": "a #2 #3", "34": "a #2 #3", "35": "a #2 #3", "36": "a #2 #3", "37": "a #2 #3", "38": "a #2 #3", "39": "a #2 #3", "40": "a #2 #3", "41": "a #4", "42": "a #4", "43": "a #4", "44": "a #4", "45": "a #4", "46": "a #4", "47": "a #4", "48": "a #4", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y", "63": "y", "64": "y", "65": "y", "66": "y", "67": "y", "68": "y", "69": "y"}, "safari": {"3.1": "a #2", "3.2": "a #2", "4": "a #2", "5": "a #2", "5.1": "a #2 #3", "6": "a #2 #3", "6.1": "a #2 #3", "7": "a #2 #3", "7.1": "a #2 #3", "8": "a #2 #3", "9": "a #2 #3", "9.1": "a #2 #3", "10": "y", "10.1": "y", "11": "y", "11.1": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "a #1", "10.5": "a #1", "10.6": "a #1", "11": "a #1", "11.1": "a #1", "11.5": "a #1", "11.6": "a #1 #3", "12": "a #1 #3", "12.1": "a #1 #3", "15": "a #2 #3", "16": "a #2 #3", "17": "a #2 #3", "18": "a #2 #3", "19": "a #2 #3", "20": "a #2 #3", "21": "a #2 #3", "22": "a #2 #3", "23": "a #2 #3", "24": "a #2 #3", "25": "a #2 #3", "26": "a #2 #3", "27": "a #2 #3", "28": "a #4", "29": "a #4", "30": "a #4", "31": "a #4", "32": "a #4", "33": "a #4", "34": "a #4", "35": "a #4", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y"}, "ios_saf": {"3.2": "a #2", "4.0-4.1": "a #2", "4.2-4.3": "a #2", "5.0-5.1": "a #2 #3", "6.0-6.1": "a #2 #3", "7.0-7.1": "a #2 #3", "8": "a #2 #3", "8.1-8.4": "a #2 #3", "9.0-9.2": "a #2 #3", "9.3": "a #2 #3", "10.0-10.2": "y", "10.3": "y", "11.0-11.2": "y", "11.3": "y"}, "op_mini": {"all": "a #1 #3"}, "android": {"2.1": "u", "2.2": "u", "2.3": "a #2", "3": "a #2 #3", "4": "a #2 #3", "4.1": "a #2 #3", "4.2-4.3": "a #2 #3", "4.4": "a #2 #3", "4.4.3-4.4.4": "a #2 #3", "62": "y"}, "bb": {"7": "a #2 #3", "10": "a #2 #3"}, "op_mob": {"10": "a #1", "11": "a #1", "11.1": "a #1", "11.5": "a #1", "12": "a #1 #3", "12.1": "a #1 #3", "37": "y"}, "and_chr": {"66": "y"}, "and_ff": {"57": "y"}, "ie_mob": {"10": "n", "11": "y"}, "and_uc": {"11.8": "y"}, "samsung": {"4": "a #4", "5": "y", "6.2": "y"}, "and_qq": {"1.2": "a #2 #3"}, "baidu": {"7.12": "a #4"}}, "notes": "", "notes_by_num": {"1": "const is recognized, but treated like var (no block scope, can be overwritten)", "2": "const does not have block scope", "3": "Only recognized when NOT in strict mode", "4": "Supported correctly in strict mode, otherwise supported without block scope"}, "usage_perc_y": 90.2, "usage_perc_a": 7.47, "ucprefix": false, "parent": "", "keywords": "ES6,constant,block,scope", "ie_id": "", "chrome_id": "4645595339816960", "firefox_id": "", "webkit_id": "", "shown": true}