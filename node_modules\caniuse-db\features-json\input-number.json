{"title": "Number input type", "description": "Form field type for numbers.", "spec": "https://html.spec.whatwg.org/multipage/forms.html#number-state-(type=number)", "status": "ls", "links": [{"url": "http://www.html5tutorial.info/html5-number.php", "title": "Tutorial"}, {"url": "https://github.com/jonstipe/number-polyfill", "title": "Polyfill"}, {"url": "https://raw.github.com/phiggins42/has.js/master/detect/form.js#input-type-number", "title": "has.js test"}, {"url": "https://www.webplatform.org/docs/html/elements/input/type/number", "title": "WebPlatform Docs"}, {"url": "https://www.slightfuture.com/webdev/html5-input-number-localization", "title": "Poor browser support for localized decimal marks, commas"}], "bugs": [{"description": "IE10 and IE11 also have broken `.valueAsNumber` results that don't follow the spec, e.g. for\r\n`input.value = \"9\"` `input.valueAsNumber` returns `NaN`."}, {"description": "The `.stepUp` and `.stepDown` methods incorrectly invoke `InvalidStateError` exceptions in IE 10 and 11."}, {"description": "IE does not convert the value to an invariant culture. IE simply submits the value as it was typed, using the user's regional settings, but the spec says the browser should submit in a normalized way."}, {"description": "Currently no mobile browsers and very few desktop browsers support using commas for languages where commas are used as decimal separators."}, {"description": "Firefox 39 in Mac OSX by default does not prevent alpha characters input and Firefox 42 validates only but It doesn't disable alpha keys input."}, {"description": "While in newer Edge versions changing the value through up/down arrow keys is supported, no `input` or `change` event is fired. See the [MS Edge bug concerning `input`](https://developer.microsoft.com/en-us/microsoft-edge/platform/issues/10305583/) and [MS Edge bug concerning `change`](https://developer.microsoft.com/en-us/microsoft-edge/platform/issues/10242461/) respectively."}], "categories": ["HTML5"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "y #1", "11": "y #1"}, "edge": {"12": "y #1", "13": "y #1", "14": "y #4", "15": "y #4", "16": "y #4", "17": "y #4", "18": "y #4"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "y #3", "30": "y #3", "31": "y #3", "32": "y #3", "33": "y #3", "34": "y #3", "35": "y #3", "36": "y #3", "37": "y #3", "38": "y #3", "39": "y #3", "40": "y #3", "41": "y #3", "42": "y #3", "43": "y #3", "44": "y #3", "45": "y #3", "46": "y #3", "47": "y #3", "48": "y #3", "49": "y #3", "50": "y #3", "51": "y #3", "52": "y #3", "53": "y #3", "54": "y #3", "55": "y #3", "56": "y #3", "57": "y #3", "58": "y #3", "59": "y #3", "60": "y #3", "61": "y #3"}, "chrome": {"4": "n", "5": "n", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y", "63": "y", "64": "y", "65": "y", "66": "y", "67": "y", "68": "y", "69": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "y", "5.1": "y", "6": "y", "6.1": "y", "7": "y", "7.1": "y", "8": "y", "9": "y", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "11.1": "y", "TP": "y"}, "opera": {"9": "y", "9.5-9.6": "y", "10.0-10.1": "y", "10.5": "y", "10.6": "y", "11": "y", "11.1": "y", "11.5": "y", "11.6": "y", "12": "y", "12.1": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y"}, "ios_saf": {"3.2": "a #1 #2", "4.0-4.1": "a #1 #2", "4.2-4.3": "a #1 #2", "5.0-5.1": "a #1 #2", "6.0-6.1": "a #1 #2", "7.0-7.1": "a #1 #2", "8": "a #1 #2", "8.1-8.4": "a #1 #2", "9.0-9.2": "a #1 #2", "9.3": "a #1 #2", "10.0-10.2": "a #1 #2", "10.3": "a #1 #2", "11.0-11.2": "a #1 #2", "11.3": "a #1 #2"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "a #1 #2", "4.1": "a #1 #2", "4.2-4.3": "a #1 #2", "4.4": "a #1 #2", "4.4.3-4.4.4": "a #1 #2", "62": "a #1 #2"}, "bb": {"7": "n", "10": "a #1 #2"}, "op_mob": {"10": "y", "11": "y", "11.1": "y", "11.5": "y", "12": "y", "12.1": "y", "37": "a #1 #2"}, "and_chr": {"66": "a #1 #2"}, "and_ff": {"57": "y #1 #3"}, "ie_mob": {"10": "a #1 #2", "11": "a #1 #2"}, "and_uc": {"11.8": "a #1 #2"}, "samsung": {"4": "a #1 #2", "5": "a #1 #2", "6.2": "a #1 #2"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "a #1 #2"}}, "notes": "", "notes_by_num": {"1": "UI widget does not include increment/decrement buttons.", "2": "UI widget does not take the \"step\", \"min\" or \"max\" attributes into account.", "3": "Firefox doesn't support [autocomplete content via datalist](https://codepen.io/graste/pen/bNoVKW) elements.", "4": "Does not include increment/decrement buttons, but does supports increment/decrement via arrow up & down keys."}, "usage_perc_y": 41.96, "usage_perc_a": 53, "ucprefix": false, "parent": "forms", "keywords": "spinner,input type=\"number\"", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}