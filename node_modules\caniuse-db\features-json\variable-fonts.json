{"title": "Variable fonts", "description": "OpenType font settings that allows a single font file to behave like multiple fonts: it can contain all the allowed variations in width, weight, slant, optical size, or any other exposed axes of variation as defined by the font designer. Variations can be applied via the `font-variation-settings` property.", "spec": "https://drafts.csswg.org/css-fonts-4/#font-variation-settings-def", "status": "wd", "links": [{"url": "http://www.axis-praxis.org/about", "title": "Axis-Praxs - Tool & info on variable fonts"}, {"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/font-variation-settings", "title": "MDN Web docs article"}, {"url": "https://medium.com/clear-left-thinking/how-to-use-variable-fonts-in-the-real-world-e6d73065a604", "title": "How to use variable fonts in the real world"}], "bugs": [{"description": "Chrome supports `format('*-variations')` inside the `@font-face` block only starting from Chrome version 66."}], "categories": ["CSS"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "y", "18": "y"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n", "46": "n", "47": "n", "48": "n", "49": "n", "50": "n", "51": "n", "52": "n", "53": "n d #2 #4 #5", "54": "n d #2 #4 #5", "55": "n d #2 #4 #5", "56": "n d #2 #4 #5", "57": "n d #2 #4 #5", "58": "n d #2 #4 #5", "59": "n d #2 #4 #5", "60": "n d #2 #4 #5", "61": "n d #2 #4 #5"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n", "46": "n", "47": "n", "48": "n", "49": "n", "50": "n", "51": "n", "52": "n", "53": "n", "54": "n", "55": "n", "56": "n", "57": "n", "58": "n", "59": "n d #1", "60": "n d #1", "61": "n d #1", "62": "a #5", "63": "a #5", "64": "a #5", "65": "a #5", "66": "y", "67": "y", "68": "y", "69": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "n", "8": "n", "9": "n", "9.1": "n", "10": "n", "10.1": "n", "11": "y #3", "11.1": "y #3", "TP": "y #3"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n", "46": "n", "47": "n", "48": "n", "49": "a #5", "50": "a #5", "51": "a #5", "52": "a #5"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "n", "8.1-8.4": "n", "9.0-9.2": "n", "9.3": "n", "10.0-10.2": "n", "10.3": "n", "11.0-11.2": "y", "11.3": "y"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "62": "a #4"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "n"}, "and_chr": {"66": "y"}, "and_ff": {"57": "n"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.8": "n"}, "samsung": {"4": "n", "5": "n", "6.2": "n"}, "and_qq": {"1.2": "n"}, "baidu": {"7.12": "n"}}, "notes": "", "notes_by_num": {"1": "Works with Experimental Web Platform features enabled", "2": "Requires MacOS 10.12+ and the following about:config flags to be enabled:\r\n`layout.css.font-variations.enabled`,\r\n`gfx.downloadable_fonts.keep_variation_tables`", "3": "Requires MacOS 10.13+", "4": "Does not support the `font-weight` and `font-stretch` properties.", "5": "Does not support `format('truetype-variations')`, `format('woff-variations')`, `format('woff2-variations')`"}, "usage_perc_y": 40.72, "usage_perc_a": 25.84, "ucprefix": false, "parent": "", "keywords": "variable fonts, variation fonts, font variations", "ie_id": "fontvariationpropertieswithopentypevariablefontsupport", "chrome_id": "4708676673732608", "firefox_id": "css-font-variation-settings", "webkit_id": "feature-variation-fonts", "shown": true}