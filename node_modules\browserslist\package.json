{"name": "browserslist", "version": "2.11.3", "description": "Share target browsers between different front-end tools, like Autoprefixer, Stylelint and babel-env-preset", "keywords": ["caniuse", "browsers", "target"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "repository": "ai/browserslist", "dependencies": {"caniuse-lite": "^1.0.30000792", "electron-to-chromium": "^1.3.30"}, "bin": "./cli.js", "devDependencies": {"cross-spawn": "^5.1.0", "eslint": "^4.15.0", "eslint-ci": "^0.1.1", "eslint-config-logux": "^17.0.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-es5": "^1.2.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-jest": "^21.6.2", "eslint-plugin-node": "^5.2.1", "eslint-plugin-promise": "^3.6.0", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1", "jest": "^22.0.6", "lint-staged": "^6.0.0", "pre-commit": "^1.1.3", "size-limit": "^0.14.0", "yaspeller-ci": "^1.0.0"}, "eslintConfig": {"extends": "eslint-config-logux/browser", "rules": {"security/detect-unsafe-regex": "off", "global-require": "off"}, "overrides": {"files": ["*.test.js"], "rules": {"es5/no-arrow-functions": "off"}}}, "jest": {"testEnvironment": "node", "coverageThreshold": {"global": {"statements": 100}}, "modulePathIgnorePatterns": ["<rootDir>/test/fixtures"]}, "size-limit": [{"path": "index.js", "limit": "160 KB"}], "scripts": {"lint-staged": "lint-staged", "spellcheck": "yaspeller-ci README.md CHANGELOG.md", "lint": "eslint-ci *.js test/*.js __mocks__/*.js", "test": "jest --coverage && yarn lint && yarn spellcheck && size-limit"}, "lint-staged": {"*.md": "yaspeller-ci", "*.js": "eslint"}, "browser": {"./node.js": "./browser.js", "path": false}, "pre-commit": ["lint-staged"]}