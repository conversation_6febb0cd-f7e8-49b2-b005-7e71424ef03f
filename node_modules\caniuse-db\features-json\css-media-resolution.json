{"title": "Media Queries: resolution feature", "description": "Allows a media query to be set based on the device pixels used per CSS unit. While the standard uses `min`/`max-resolution` for this, some browsers support the older non-standard `device-pixel-ratio` media query.", "spec": "https://www.w3.org/TR/css3-mediaqueries/#resolution", "status": "rec", "links": [{"url": "https://www.w3.org/blog/CSS/2012/06/14/unprefix-webkit-device-pixel-ratio/", "title": "How to unprefix -webkit-device-pixel-ratio"}, {"url": "https://bugs.webkit.org/show_bug.cgi?id=78087", "title": "WebKit Bug 78087: Implement the 'resolution' media query"}, {"url": "https://compat.spec.whatwg.org/#css-media-queries-webkit-device-pixel-ratio", "title": "WHATWG Compatibility Standard: -webkit-device-pixel-ratio"}], "bugs": [{"description": "Microsoft Edge has a bug where `min-resolution` less than `1dpcm` [is ignored](https://jsfiddle.net/behmjd5t/)."}], "categories": ["CSS", "CSS3"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "a #1", "10": "a #1", "11": "a #1"}, "edge": {"12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y"}, "firefox": {"2": "n", "3": "n", "3.5": "a #2", "3.6": "a #2", "4": "a #2", "5": "a #2", "6": "a #2", "7": "a #2", "8": "a #2", "9": "a #2", "10": "a #2", "11": "a #2", "12": "a #2", "13": "a #2", "14": "a #2", "15": "a #2", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y"}, "chrome": {"4": "a x #3", "5": "a x #3", "6": "a x #3", "7": "a x #3", "8": "a x #3", "9": "a x #3", "10": "a x #3", "11": "a x #3", "12": "a x #3", "13": "a x #3", "14": "a x #3", "15": "a x #3", "16": "a x #3", "17": "a x #3", "18": "a x #3", "19": "a x #3", "20": "a x #3", "21": "a x #3", "22": "a x #3", "23": "a x #3", "24": "a x #3", "25": "a x #3", "26": "a x #3", "27": "a x #3", "28": "a x #3", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y", "63": "y", "64": "y", "65": "y", "66": "y", "67": "y", "68": "y", "69": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "a x #3", "5": "a x #3", "5.1": "a x #3", "6": "a x #3", "6.1": "a x #3", "7": "a x #3", "7.1": "a x #3", "8": "a x #3", "9": "a x #3", "9.1": "a x #3", "10": "a x #3", "10.1": "a x #3", "11": "a x #3", "11.1": "a x #3", "TP": "a x #3"}, "opera": {"9": "n", "9.5-9.6": "a x #3", "10.0-10.1": "a x #3", "10.5": "a x #3", "10.6": "a x #3", "11": "a x #3", "11.1": "a x #3", "11.5": "a x #3", "11.6": "a x #3", "12": "a x #3", "12.1": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y"}, "ios_saf": {"3.2": "u", "4.0-4.1": "a x #3", "4.2-4.3": "a x #3", "5.0-5.1": "a x #3", "6.0-6.1": "a x #3", "7.0-7.1": "a x #3", "8": "a x #3", "8.1-8.4": "a x #3", "9.0-9.2": "a x #3", "9.3": "a x #3", "10.0-10.2": "a x #3", "10.3": "a x #3", "11.0-11.2": "a x #3", "11.3": "a x #3"}, "op_mini": {"all": "a #1"}, "android": {"2.1": "u", "2.2": "u", "2.3": "a x #3", "3": "a x #3", "4": "a x #3", "4.1": "a x #3", "4.2-4.3": "a x #3", "4.4": "y", "4.4.3-4.4.4": "y", "62": "y"}, "bb": {"7": "a x #3", "10": "a x #3"}, "op_mob": {"10": "a x #3", "11": "a x #3", "11.1": "a x #3", "11.5": "a x #3", "12": "a x #3", "12.1": "y", "37": "y"}, "and_chr": {"66": "y"}, "and_ff": {"57": "y"}, "ie_mob": {"10": "a #1", "11": "a #1"}, "and_uc": {"11.8": "y"}, "samsung": {"4": "y", "5": "y", "6.2": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "", "notes_by_num": {"1": "Supports the `dpi` unit, but does not support `dppx` or `dpcm` units.", "2": "Firefox before 16 supports only `dpi` unit, but you can set `2dppx` per `min--moz-device-pixel-ratio: 2`", "3": "Supports the non-standard `min`/`max-device-pixel-ratio`"}, "usage_perc_y": 78.31, "usage_perc_a": 19.62, "ucprefix": false, "parent": "css-mediaqueries", "keywords": "@media,device-pixel-ratio,resolution,dppx,dpcm,dpi", "ie_id": "mediaqueriesresolutionfeature,dppxunitfortheresolutionmediaquery", "chrome_id": "5944509615570944", "firefox_id": "", "webkit_id": "", "shown": true}