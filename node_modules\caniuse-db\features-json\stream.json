{"title": "getUserMedia/Stream API", "description": "Method of accessing external device data (such as a webcam video stream). Formerly this was envisioned as the <device> element.", "spec": "https://www.w3.org/TR/mediacapture-streams/", "status": "cr", "links": [{"url": "https://dev.opera.com/blog/webcam-orientation-preview/", "title": "Technology preview from Opera"}, {"url": "https://www.webplatform.org/docs/dom/Navigator/getUserMedia", "title": "WebPlatform Docs"}, {"url": "https://blogs.windows.com/msedgedev/2015/05/13/announcing-media-capture-functionality-in-microsoft-edge/", "title": "Media Capture functionality in Microsoft Edge"}], "bugs": [{"description": "Until version 12.16, Opera supported [video context only](http://my.opera.com/community/forums/topic.dml?id=1528992)."}], "categories": ["JS API"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "a x #1 #2", "18": "a x #1 #2", "19": "a x #1 #2", "20": "a x #1 #2", "21": "a x #1 #2", "22": "a x #1 #2", "23": "a x #1 #2", "24": "a x #1 #2", "25": "a x #1 #2", "26": "a x #1 #2", "27": "a x #1 #2", "28": "a x #1 #2", "29": "a x #1 #2", "30": "a x #1 #2", "31": "a x #1 #2", "32": "a x #1 #2", "33": "a x #1 #2", "34": "a x #1 #2", "35": "a x #1 #2", "36": "y #1", "37": "y #1", "38": "y #1", "39": "y #1", "40": "y #1", "41": "y #1", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "a x #1 #2", "22": "a x #1 #2", "23": "a x #1 #2", "24": "a x #1 #2", "25": "a x #1 #2", "26": "a x #1 #2", "27": "a x #1 #2", "28": "a x #1 #2", "29": "a x #1 #2", "30": "a x #1 #2", "31": "a x #1 #2", "32": "a x #1 #2", "33": "a x #1 #2", "34": "a x #1 #2", "35": "a x #1 #2", "36": "a x #1 #2", "37": "a x #1 #2", "38": "a x #1 #2", "39": "a x #1 #2", "40": "a x #1 #2", "41": "a x #1 #2", "42": "a x #1 #2", "43": "a x #1 #2", "44": "a x #1 #2", "45": "a x #1 #2", "46": "a x #1 #2", "47": "a x #1 #2", "48": "a x #1 #2", "49": "a x #1 #2", "50": "a x #1 #2", "51": "a x #1 #2", "52": "a x #1 #2", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y", "63": "y", "64": "y", "65": "y", "66": "y", "67": "y", "68": "y", "69": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "n", "8": "n", "9": "n", "9.1": "n", "10": "n", "10.1": "n", "11": "y", "11.1": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "a x #1 #2", "12.1": "a x #1 #2", "15": "n", "16": "n", "17": "n", "18": "a x #1 #2", "19": "a x #1 #2", "20": "a x #1 #2", "21": "a x #1 #2", "22": "a x #1 #2", "23": "a x #1 #2", "24": "a x #1 #2", "25": "a x #1 #2", "26": "a x #1 #2", "27": "a x #1 #2", "28": "a x #1 #2", "29": "a x #1 #2", "30": "a x #1 #2", "31": "a x #1 #2", "32": "a x #1 #2", "33": "a x #1 #2", "34": "a x #1 #2", "35": "a x #1 #2", "36": "a x #1 #2", "37": "a x #1 #2", "38": "a x #1 #2", "39": "a x #1 #2", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "n", "8.1-8.4": "n", "9.0-9.2": "n", "9.3": "n", "10.0-10.2": "n", "10.3": "n", "11.0-11.2": "y", "11.3": "y"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "62": "y"}, "bb": {"7": "n", "10": "a x #1 #2"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "a x #1 #2", "12.1": "a x #1 #2", "37": "y"}, "and_chr": {"66": "y"}, "and_ff": {"57": "y"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.8": "y"}, "samsung": {"4": "a x #1 #2", "5": "a x #1 #2", "6.2": "y"}, "and_qq": {"1.2": "a x #1 #2"}, "baidu": {"7.12": "a x #1 #2"}}, "notes": "As of Chrome 47, the getUserMedia API cannot be called from insecure origins.", "notes_by_num": {"1": "Blink-based (and some other) browsers support an older version of the spec that does not use `srcObject`. [See Chromium issue 387740](https://code.google.com/p/chromium/issues/detail?id=387740).", "2": "Supports the older spec's `navigator.getUserMedia` API, not the newer `navigator.mediaDevices.getUserMedia` one."}, "usage_perc_y": 84.18, "usage_perc_a": 3.51, "ucprefix": false, "parent": "", "keywords": "camera,device,getUserMedia,media stream,mediastream,Media Capture API", "ie_id": "mediacaptureandstreams", "chrome_id": "6067380039974912,6605041225957376", "firefox_id": "", "webkit_id": "", "shown": true}