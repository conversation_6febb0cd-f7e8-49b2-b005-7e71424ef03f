{"title": "CSSOM Scroll-behavior", "description": "Method of specifying the scrolling behavior for a scrolling box, when scrolling happens due to navigation or CSSOM scrolling APIs.", "spec": "https://drafts.csswg.org/cssom-view/#propdef-scroll-behavior", "status": "wd", "links": [{"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/scroll-behavior", "title": "MDN Web Docs - CSS scroll-behavior"}, {"url": "https://code.google.com/p/chromium/issues/detail?id=243871", "title": "Chrome launch bug "}, {"url": "https://blog.gospodarets.com/native_smooth_scrolling", "title": "Blog post with demo"}], "bugs": [], "categories": ["CSS"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n d #1 #2", "42": "n d #1 #2", "43": "n d #1 #2", "44": "n d #1 #2", "45": "n d #1 #2", "46": "n d #1 #2", "47": "n d #1 #2", "48": "n d #1 #2", "49": "n d #1 #2", "50": "n d #1 #2", "51": "n d #1 #2", "52": "n d #1 #2", "53": "n d #1 #2", "54": "n d #1 #2", "55": "n d #1 #2", "56": "n d #1 #2", "57": "n d #1 #2", "58": "n d #1 #2", "59": "n d #1 #2", "60": "n d #1 #2", "61": "y #1", "62": "y #1", "63": "y #1", "64": "y #1", "65": "y #1", "66": "y #1", "67": "y #1", "68": "y #1", "69": "y #1"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "n", "8": "n", "9": "n", "9.1": "n", "10": "n", "10.1": "n", "11": "n", "11.1": "n", "TP": "n"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n d #1 #2", "29": "n d #1 #2", "30": "n d #1 #2", "31": "n d #1 #2", "32": "n d #1 #2", "33": "n d #1 #2", "34": "n d #1 #2", "35": "n d #1 #2", "36": "n d #1 #2", "37": "n d #1 #2", "38": "n d #1 #2", "39": "n d #1 #2", "40": "n d #1 #2", "41": "n d #1 #2", "42": "n d #1 #2", "43": "n d #1 #2", "44": "n d #1 #2", "45": "n d #1 #2", "46": "n d #1 #2", "47": "n d #1 #2", "48": "y #1", "49": "y #1", "50": "y #1", "51": "y #1", "52": "y #1"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "n", "8.1-8.4": "n", "9.0-9.2": "n", "9.3": "n", "10.0-10.2": "n", "10.3": "n", "11.0-11.2": "n", "11.3": "n"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "62": "n"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "n"}, "and_chr": {"66": "y"}, "and_ff": {"57": "n"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.8": "n"}, "samsung": {"4": "n", "5": "n", "6.2": "n"}, "and_qq": {"1.2": "n d #1 #2"}, "baidu": {"7.12": "n"}}, "notes": "", "notes_by_num": {"1": "Partial support refers to support everything except of `Element.scrollIntoView()` and not together with pinch viewport.", "2": "Supported in Chrome and Opera behind the 'Smooth Scrolling' and/or 'Enable experimental web platform features' flag"}, "usage_perc_y": 61.45, "usage_perc_a": 0, "ucprefix": false, "parent": "", "keywords": "smooth,scroll,cssom,scroll-behavior", "ie_id": "cssomviewsmoothscrollapi", "chrome_id": "5812155903377408", "firefox_id": "", "webkit_id": "", "shown": true}