{"name": "babel-plugin-transform-es2015-modules-systemjs", "version": "6.24.1", "description": "This plugin transforms ES2015 modules to SystemJS", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-es2015-modules-systemjs", "license": "MIT", "main": "lib/index.js", "dependencies": {"babel-template": "^6.24.1", "babel-helper-hoist-variables": "^6.24.1", "babel-runtime": "^6.22.0"}, "keywords": ["babel-plugin"], "devDependencies": {"babel-helper-plugin-test-runner": "^6.24.1", "babel-plugin-syntax-dynamic-import": "^6.18.0"}}