{"title": "accept attribute for file input", "description": "Allows a filter to be defined for what type of files a user may pick with from an `<input type=\"file\">` dialog", "spec": "https://html.spec.whatwg.org/multipage/forms.html#attr-input-accept", "status": "ls", "links": [{"url": "https://www.wufoo.com/html5/attributes/07-accept.html", "title": "Demo & information"}, {"url": "https://wpdev.uservoice.com/forums/257854-microsoft-edge-developer/suggestions/13661175-full-spec-support-for-accept-in-input-type-file", "title": "Microsoft Edge feature request on UserVoice"}], "bugs": [], "categories": ["HTML5"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "y", "11": "y"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "a #1", "5": "a #1", "6": "a #1", "7": "a #1", "8": "a #1", "9": "a #1", "10": "a #1", "11": "a #1", "12": "a #1", "13": "a #1", "14": "a #1", "15": "a #1", "16": "a #1", "17": "a #1", "18": "a #1", "19": "a #1", "20": "a #1", "21": "a #1", "22": "a #1", "23": "a #1", "24": "a #1", "25": "a #1", "26": "a #1", "27": "a #1", "28": "a #1", "29": "a #1", "30": "a #1", "31": "a #1", "32": "a #1", "33": "a #1", "34": "a #1", "35": "a #1", "36": "a #1", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y"}, "chrome": {"4": "n", "5": "u", "6": "u", "7": "u", "8": "u", "9": "a #1", "10": "a #1", "11": "a #1", "12": "a #1", "13": "a #1", "14": "a #1", "15": "a #1", "16": "a #1", "17": "a #1", "18": "a #1", "19": "a #1", "20": "a #1", "21": "u", "22": "u", "23": "u", "24": "u", "25": "u", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y", "63": "y", "64": "y", "65": "y", "66": "y", "67": "y", "68": "y", "69": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "a #1", "6.1": "a #1", "7": "a #1", "7.1": "a #1", "8": "a #1", "9": "a #1", "9.1": "a #1", "10": "a #1", "10.1": "a #1", "11": "a #1", "11.1": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y"}, "ios_saf": {"3.2": "n #3", "4.0-4.1": "n #3", "4.2-4.3": "n #3", "5.0-5.1": "n #3", "6.0-6.1": "n", "7.0-7.1": "n", "8": "a #1", "8.1-8.4": "a #1", "9.0-9.2": "a #1", "9.3": "a #1", "10.0-10.2": "a #1", "10.3": "a #1", "11.0-11.2": "a #1", "11.3": "a #1"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "a #2", "4": "a #2", "4.1": "a #2", "4.2-4.3": "a #2", "4.4": "n #3", "4.4.3-4.4.4": "n #3", "62": "n #3"}, "bb": {"7": "a #2", "10": "a #1"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "a #2"}, "and_chr": {"66": "a #2"}, "and_ff": {"57": "n"}, "ie_mob": {"10": "n #3", "11": "a #4"}, "and_uc": {"11.8": "n"}, "samsung": {"4": "a #2", "5": "a #2", "6.2": "a #2"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "a #2"}}, "notes": "Not supported means any file can be picked as if the `accept` attribute was not set, unless otherwise noted.\r\n\r\nOn Windows, files that do not apply are hidden. On OSX they are grayed out and disabled.", "notes_by_num": {"1": "Supports the type format (e.g. `image/*`) but not the extension format (e.g. `.png`)", "2": "Offers appropriate file locations/input based on format type, but does not prevent other files from being selected.", "3": "Does not allow any files to be picked at all", "4": "Supports the type format but does not allow any file to be picked when using the extension format"}, "usage_perc_y": 37.17, "usage_perc_a": 46.98, "ucprefix": false, "parent": "", "keywords": "", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}