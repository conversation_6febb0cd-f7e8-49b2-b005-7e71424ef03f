{"title": "::placeholder CSS pseudo-element", "description": "The ::placeholder pseudo-element represents placeholder text in an input field: text that represents the input and provides a hint to the user on how to fill out the form. For example, a date-input field might have the placeholder text `YYYY/MM/DD` to clarify that numeric dates are to be entered in year-month-day order.", "spec": "https://drafts.csswg.org/css-pseudo-4/#placeholder-pseudo", "status": "wd", "links": [{"url": "http://msdn.microsoft.com/en-us/library/ie/hh772745(v=vs.85).aspx", "title": "MSDN article"}, {"url": "https://css-tricks.com/snippets/css/style-placeholder-text/", "title": "CSS-Tricks article with all prefixes"}, {"url": "http://wiki.csswg.org/ideas/placeholder-styling", "title": "CSSWG discussion"}, {"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/::-moz-placeholder", "title": "MDN Web Docs - CSS ::-moz-placeholder"}, {"url": "https://bugzilla.mozilla.org/show_bug.cgi?id=1069012", "title": "Mozilla Bug 1069012 - unprefix :placeholder-shown pseudo-class and ::placeholder pseudo-element"}, {"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/::placeholder", "title": "MDN web docs - ::placeholder"}], "bugs": [], "categories": ["CSS"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "a x", "11": "a x"}, "edge": {"12": "a x", "13": "a x", "14": "a x", "15": "a x", "16": "a x", "17": "a x", "18": "a x"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "a x #1", "5": "a x #1", "6": "a x #1", "7": "a x #1", "8": "a x #1", "9": "a x #1", "10": "a x #1", "11": "a x #1", "12": "a x #1", "13": "a x #1", "14": "a x #1", "15": "a x #1", "16": "a x #1", "17": "a x #1", "18": "a x #1", "19": "y x", "20": "y x", "21": "y x", "22": "y x", "23": "y x", "24": "y x", "25": "y x", "26": "y x", "27": "y x", "28": "y x", "29": "y x", "30": "y x", "31": "y x", "32": "y x", "33": "y x", "34": "y x", "35": "y x", "36": "y x", "37": "y x", "38": "y x", "39": "y x", "40": "y x", "41": "y x", "42": "y x", "43": "y x", "44": "y x", "45": "y x", "46": "y x", "47": "y x", "48": "y x", "49": "y x", "50": "y x", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y"}, "chrome": {"4": "a x", "5": "a x", "6": "a x", "7": "a x", "8": "a x", "9": "a x", "10": "a x", "11": "a x", "12": "a x", "13": "a x", "14": "a x", "15": "a x", "16": "a x", "17": "a x", "18": "a x", "19": "a x", "20": "a x", "21": "a x", "22": "a x", "23": "a x", "24": "a x", "25": "a x", "26": "a x", "27": "a x", "28": "a x", "29": "a x", "30": "a x", "31": "a x", "32": "a x", "33": "a x", "34": "a x", "35": "a x", "36": "a x", "37": "a x", "38": "a x", "39": "a x", "40": "a x", "41": "a x", "42": "a x", "43": "a x", "44": "a x", "45": "a x", "46": "a x", "47": "a x", "48": "a x", "49": "a x", "50": "a x", "51": "a x", "52": "a x", "53": "a x", "54": "a x", "55": "a x", "56": "a x", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y", "63": "y", "64": "y", "65": "y", "66": "y", "67": "y", "68": "y", "69": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "a x", "5.1": "a x", "6": "a x", "6.1": "a x", "7": "a x", "7.1": "a x", "8": "a x", "9": "a x", "9.1": "a x", "10": "a x", "10.1": "y", "11": "y", "11.1": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "a x", "16": "a x", "17": "a x", "18": "a x", "19": "a x", "20": "a x", "21": "a x", "22": "a x", "23": "a x", "24": "a x", "25": "a x", "26": "a x", "27": "a x", "28": "a x", "29": "a x", "30": "a x", "31": "a x", "32": "a x", "33": "a x", "34": "a x", "35": "a x", "36": "a x", "37": "a x", "38": "a x", "39": "a x", "40": "a x", "41": "a x", "42": "a x", "43": "a x", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "a x", "5.0-5.1": "a x", "6.0-6.1": "a x", "7.0-7.1": "a x", "8": "a x", "8.1-8.4": "a x", "9.0-9.2": "a x", "9.3": "a x", "10.0-10.2": "a x", "10.3": "y", "11.0-11.2": "y", "11.3": "y"}, "op_mini": {"all": "n"}, "android": {"2.1": "a x", "2.2": "a x", "2.3": "a x", "3": "a x", "4": "a x", "4.1": "a x", "4.2-4.3": "a x", "4.4": "a x", "4.4.3-4.4.4": "a x", "62": "y"}, "bb": {"7": "a x", "10": "a x"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "a x"}, "and_chr": {"66": "y"}, "and_ff": {"57": "y"}, "ie_mob": {"10": "a x", "11": "a x"}, "and_uc": {"11.8": "y"}, "samsung": {"4": "a x", "5": "a x", "6.2": "a x"}, "and_qq": {"1.2": "a x"}, "baidu": {"7.12": "y"}}, "notes": "Partial support refers to using alternate names:\r\n`::-webkit-input-placeholder` for Chrome/Safari/Opera ([Chrome issue #623345](https://bugs.chromium.org/p/chromium/issues/detail?id=623345))\r\n`:-ms-input-placeholder` for IE. \r\n`::-ms-input-placeholder` for Edge (also supports webkit prefix)", "notes_by_num": {"1": "Firefox 18 and below supported the `:-moz-placeholder` pseudo-class rather than the `::-moz-placeholder` pseudo-element."}, "usage_perc_y": 81.39, "usage_perc_a": 13.63, "ucprefix": false, "parent": "", "keywords": "::placeholder,placeholder", "ie_id": "", "chrome_id": "6715780926275584", "firefox_id": "", "webkit_id": "", "shown": true}