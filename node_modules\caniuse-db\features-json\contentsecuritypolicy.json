{"title": "Content Security Policy 1.0", "description": "Mitigate cross-site scripting attacks by whitelisting allowed sources of script, style, and other resources.", "spec": "https://www.w3.org/TR/2012/CR-CSP-20121115/", "status": "cr", "links": [{"url": "https://www.html5rocks.com/en/tutorials/security/content-security-policy/", "title": "HTML5Rocks article"}, {"url": "http://content-security-policy.com/", "title": "CSP Examples & Quick Reference"}, {"url": "https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP", "title": "MDN Web Docs - Content Security Policy"}], "bugs": [{"description": "Partial support in Internet Explorer 10-11 refers to the browser only supporting the 'sandbox' directive by using the `X-Content-Security-Policy` header."}, {"description": "Partial support in iOS Safari 5.0-5.1 refers to the browser recognizing the `X-WebKit-CSP` header but failing to handle complex cases correctly, often resulting in broken pages."}, {"description": "Chrome for iOS fails to render pages without a [connect-src 'self'](https://code.google.com/p/chromium/issues/detail?id=322497) policy."}], "categories": ["Security"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "a #1", "11": "a #1"}, "edge": {"12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "y #1", "5": "y #1", "6": "y #1", "7": "y #1", "8": "y #1", "9": "y #1", "10": "y #1", "11": "y #1", "12": "y #1", "13": "y #1", "14": "y #1", "15": "y #1", "16": "y #1", "17": "y #1", "18": "y #1", "19": "y #1", "20": "y #1", "21": "y #1", "22": "y #1", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "y #2", "15": "y #2", "16": "y #2", "17": "y #2", "18": "y #2", "19": "y #2", "20": "y #2", "21": "y #2", "22": "y #2", "23": "y #2", "24": "y #2", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y", "63": "y", "64": "y", "65": "y", "66": "y", "67": "y", "68": "y", "69": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "a #2", "6": "y #2", "6.1": "y #2", "7": "y", "7.1": "y", "8": "y", "9": "y", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "11.1": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "a #2", "6.0-6.1": "y #2", "7.0-7.1": "y", "8": "y", "8.1-8.4": "y", "9.0-9.2": "y", "9.3": "y", "10.0-10.2": "y", "10.3": "y", "11.0-11.2": "y", "11.3": "y"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "y", "4.4.3-4.4.4": "y", "62": "y"}, "bb": {"7": "n", "10": "y #2"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "y"}, "and_chr": {"66": "y"}, "and_ff": {"57": "y"}, "ie_mob": {"10": "a #1", "11": "a #1"}, "and_uc": {"11.8": "y #2"}, "samsung": {"4": "y", "5": "y", "6.2": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "The standard HTTP header is `Content-Security-Policy` which is used unless otherwise noted.", "notes_by_num": {"1": "Supported through the `X-Content-Security-Policy` header", "2": "Supported through the `X-WebKit-CSP` header"}, "usage_perc_y": 91.48, "usage_perc_a": 3.18, "ucprefix": false, "parent": "", "keywords": "csp,security,header", "ie_id": "contentsecuritypolicy", "chrome_id": "5205088045891584", "firefox_id": "", "webkit_id": "", "shown": true}