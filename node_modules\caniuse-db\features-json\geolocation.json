{"title": "Geolocation", "description": "Method of informing a website of the user's geographical location", "spec": "https://www.w3.org/TR/geolocation-API/", "status": "rec", "links": [{"url": "http://html5demos.com/geo", "title": "Simple demo"}, {"url": "https://raw.github.com/phiggins42/has.js/master/detect/features.js#native-geolocation", "title": "has.js test"}, {"url": "https://www.webplatform.org/docs/apis/geolocation", "title": "WebPlatform Docs"}], "bugs": [{"description": "IE9 appears to [have some issues](http://social.technet.microsoft.com/Forums/en-IE/ieitprocurrentver/thread/aea4db4e-0720-44fe-a9b8-09917e345080) in correctly determining longitude/latitude."}, {"description": "iOS6 has problems with returning [high accuracy data](https://discussions.apple.com/thread/4313850?start=0&tstart=0)."}, {"description": "Safari 5 & 6 seem to not provide geolocation data [when using a wired connection](https://stackoverflow.com/questions/3791442/geolocation-in-safari-5)."}, {"description": "Firefox 52 and below had a permission dialog with an additional \"not now\" option. This option postponed the permission decision until later and therefore deliberately did not call any callbacks, [see bug](https://bugzilla.mozilla.org/show_bug.cgi?id=675533)"}], "categories": ["JS API"], "stats": {"ie": {"5.5": "n", "6": "p", "7": "p", "8": "p", "9": "y", "10": "y", "11": "y"}, "edge": {"12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y"}, "firefox": {"2": "p", "3": "p", "3.5": "y", "3.6": "y", "4": "y", "5": "y", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y #1", "56": "y #1", "57": "y #1", "58": "y #1", "59": "y #1", "60": "y #1", "61": "y #1"}, "chrome": {"4": "a", "5": "y", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y #1", "51": "y #1", "52": "y #1", "53": "y #1", "54": "y #1", "55": "y #1", "56": "y #1", "57": "y #1", "58": "y #1", "59": "y #1", "60": "y #1", "61": "y #1", "62": "y #1", "63": "y #1", "64": "y #1", "65": "y #1", "66": "y #1", "67": "y #1", "68": "y #1", "69": "y #1"}, "safari": {"3.1": "p", "3.2": "p", "4": "p", "5": "y", "5.1": "y", "6": "y", "6.1": "y", "7": "y", "7.1": "y", "8": "y", "9": "y", "9.1": "y", "10": "y #1", "10.1": "y", "11": "y", "11.1": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "p", "10.5": "p", "10.6": "y", "11": "y", "11.1": "y", "11.5": "y", "11.6": "y", "12": "y", "12.1": "y", "15": "n", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y #1", "40": "y #1", "41": "y #1", "42": "y #1", "43": "y #1", "44": "y #1", "45": "y #1", "46": "y #1", "47": "y #1", "48": "y #1", "49": "y #1", "50": "y #1", "51": "y #1", "52": "y #1"}, "ios_saf": {"3.2": "y", "4.0-4.1": "y", "4.2-4.3": "y", "5.0-5.1": "y", "6.0-6.1": "y", "7.0-7.1": "y", "8": "y", "8.1-8.4": "y", "9.0-9.2": "y", "9.3": "y", "10.0-10.2": "y #1", "10.3": "y #1", "11.0-11.2": "y #1", "11.3": "y #1"}, "op_mini": {"all": "n"}, "android": {"2.1": "y", "2.2": "y", "2.3": "y", "3": "y", "4": "y", "4.1": "y", "4.2-4.3": "y", "4.4": "y", "4.4.3-4.4.4": "y", "62": "y #1"}, "bb": {"7": "y", "10": "y"}, "op_mob": {"10": "p", "11": "y", "11.1": "y", "11.5": "y", "12": "y", "12.1": "y", "37": "y"}, "and_chr": {"66": "y #1"}, "and_ff": {"57": "y #1"}, "ie_mob": {"10": "y", "11": "y"}, "and_uc": {"11.8": "y"}, "samsung": {"4": "y", "5": "y #1", "6.2": "y #1"}, "and_qq": {"1.2": "y #1"}, "baidu": {"7.12": "y #1"}}, "notes": "", "notes_by_num": {"1": "Only works on secure (https) servers"}, "usage_perc_y": 95.23, "usage_perc_a": 0, "ucprefix": false, "parent": "", "keywords": "", "ie_id": "geolocation", "chrome_id": "6348855016685568", "firefox_id": "", "webkit_id": "", "shown": true}