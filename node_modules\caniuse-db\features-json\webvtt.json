{"title": "WebVTT - Web Video Text Tracks", "description": "Format for marking up text captions for multimedia resources.", "spec": "https://w3c.github.io/webvtt/", "status": "wd", "links": [{"url": "https://www.html5rocks.com/en/tutorials/track/basics/", "title": "Getting Started With the Track Element"}, {"url": "https://dev.opera.com/articles/view/an-introduction-to-webvtt-and-track/", "title": "An Introduction to WebVTT and track"}, {"url": "https://developer.mozilla.org/en-US/docs/Web/API/WebVTT_API", "title": "MDN Web Docs - WebVTT"}], "bugs": [{"description": "In Firefox, captions are not visible for audio-only files in the `video` tag ([see bug](https://bugzilla.mozilla.org/show_bug.cgi?id=992664))."}, {"description": "Firefox 46 and below do not support the `textTrack.onCueChange()` event ([see details](https://stackoverflow.com/questions/28144668/html5-audio-texttrack-kind-subtitles-cuechange-event-not-working-in-firefox))."}], "categories": ["Other"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "y", "11": "y"}, "edge": {"12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n d", "25": "n d", "26": "n d", "27": "n d", "28": "n d", "29": "n d", "30": "n d", "31": "y #1", "32": "y #1", "33": "y #1", "34": "y #1", "35": "y #1", "36": "y #1", "37": "y #1", "38": "y #1", "39": "y #1", "40": "y #1", "41": "y #1", "42": "y #1", "43": "y #1", "44": "y #1", "45": "y #1", "46": "y #1", "47": "y #1", "48": "y #1", "49": "y #1", "50": "y #1", "51": "y #1", "52": "y #1", "53": "y #1", "54": "y #1", "55": "y #1", "56": "y #1", "57": "y #1", "58": "y #1", "59": "y #1", "60": "y #1", "61": "y #1"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y", "63": "y", "64": "y", "65": "y", "66": "y", "67": "y", "68": "y", "69": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "y", "6.1": "y", "7": "y", "7.1": "y", "8": "y", "9": "y", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "11.1": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "y", "8": "y", "8.1-8.4": "y", "9.0-9.2": "y", "9.3": "y", "10.0-10.2": "y", "10.3": "y", "11.0-11.2": "y", "11.3": "y"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "y", "4.4.3-4.4.4": "y", "62": "y"}, "bb": {"7": "n", "10": "y"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "y"}, "and_chr": {"66": "y"}, "and_ff": {"57": "y"}, "ie_mob": {"10": "n", "11": "y"}, "and_uc": {"11.8": "n"}, "samsung": {"4": "y", "5": "y", "6.2": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "WebVTT must be used with the <track> element.", "notes_by_num": {"1": "Firefox currently lacks support for the `::cue` pseudo-element. [See bug #865395.](https://bugzilla.mozilla.org/show_bug.cgi?id=865395)"}, "usage_perc_y": 86.83, "usage_perc_a": 0, "ucprefix": false, "parent": "video", "keywords": "captions,track,vtt,subtitles", "ie_id": "", "chrome_id": "6719115557339136", "firefox_id": "", "webkit_id": "", "shown": true}