{"title": "CSS3 Overflow-wrap", "description": "Allows lines to be broken within words if an otherwise unbreakable string is too long to fit. Currently mostly supported using the `word-wrap` property.", "spec": "https://www.w3.org/TR/css3-text/#overflow-wrap", "status": "wd", "links": [{"url": "https://developer.mozilla.org/En/CSS/Word-wrap", "title": "MDN Web Docs - CSS word-wrap"}, {"url": "https://www.webplatform.org/docs/css/properties/word-wrap", "title": "WebPlatform Docs"}, {"url": "https://bugzilla.mozilla.org/show_bug.cgi?id=955857", "title": "Bug on Firefox support"}, {"url": "https://wpdev.uservoice.com/forums/257854-microsoft-edge-developer/suggestions/6524680-update-word-wrap-to-overflow-wrap", "title": "Microsoft Edge feature request on UserVoice"}], "bugs": [], "categories": ["CSS3"], "stats": {"ie": {"5.5": "a", "6": "a", "7": "a", "8": "a", "9": "a", "10": "a", "11": "a"}, "edge": {"12": "a", "13": "a", "14": "a", "15": "a", "16": "a", "17": "a", "18": "y"}, "firefox": {"2": "n", "3": "n", "3.5": "a", "3.6": "a", "4": "a", "5": "a", "6": "a", "7": "a", "8": "a", "9": "a", "10": "a", "11": "a", "12": "a", "13": "a", "14": "a", "15": "a", "16": "a", "17": "a", "18": "a", "19": "a", "20": "a", "21": "a", "22": "a", "23": "a", "24": "a", "25": "a", "26": "a", "27": "a", "28": "a", "29": "a", "30": "a", "31": "a", "32": "a", "33": "a", "34": "a", "35": "a", "36": "a", "37": "a", "38": "a", "39": "a", "40": "a", "41": "a", "42": "a", "43": "a", "44": "a", "45": "a", "46": "a", "47": "a", "48": "a", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y"}, "chrome": {"4": "a", "5": "a", "6": "a", "7": "a", "8": "a", "9": "a", "10": "a", "11": "a", "12": "a", "13": "a", "14": "a", "15": "a", "16": "a", "17": "a", "18": "a", "19": "a", "20": "a", "21": "a", "22": "a", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y", "63": "y", "64": "y", "65": "y", "66": "y", "67": "y", "68": "y", "69": "y"}, "safari": {"3.1": "a", "3.2": "a", "4": "a", "5": "a", "5.1": "a", "6": "a", "6.1": "y", "7": "y", "7.1": "y", "8": "y", "9": "y", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "11.1": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "a", "10.6": "a", "11": "a", "11.1": "a", "11.5": "a", "11.6": "a", "12": "a", "12.1": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y"}, "ios_saf": {"3.2": "a", "4.0-4.1": "a", "4.2-4.3": "a", "5.0-5.1": "a", "6.0-6.1": "a", "7.0-7.1": "y", "8": "y", "8.1-8.4": "y", "9.0-9.2": "y", "9.3": "y", "10.0-10.2": "y", "10.3": "y", "11.0-11.2": "y", "11.3": "y"}, "op_mini": {"all": "a"}, "android": {"2.1": "a", "2.2": "a", "2.3": "a", "3": "a", "4": "a", "4.1": "a", "4.2-4.3": "a", "4.4": "y", "4.4.3-4.4.4": "y", "62": "y"}, "bb": {"7": "a", "10": "y"}, "op_mob": {"10": "a", "11": "a", "11.1": "a", "11.5": "a", "12": "a", "12.1": "a", "37": "y"}, "and_chr": {"66": "y"}, "and_ff": {"57": "y"}, "ie_mob": {"10": "a", "11": "a"}, "and_uc": {"11.8": "y"}, "samsung": {"4": "y", "5": "y", "6.2": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "Partial support refers to requiring the legacy name \"word-wrap\" (rather than \"overflow-wrap\") to work.", "notes_by_num": {}, "usage_perc_y": 88.9, "usage_perc_a": 9.23, "ucprefix": false, "parent": "", "keywords": "wordwrap,word-wrap", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}