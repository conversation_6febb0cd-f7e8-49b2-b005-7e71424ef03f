{"title": "HTTP/2 protocol", "description": "Networking protocol for low-latency transport of content over the web. Originally started out from the SPDY protocol, now standardized as HTTP version 2.", "spec": "http://http2.github.io/http2-spec/index.html", "status": "other", "links": [{"url": "https://en.wikipedia.org/wiki/HTTP/2", "title": "Wikipedia"}, {"url": "https://http2.akamai.com/demo", "title": "Browser support test"}], "bugs": [], "categories": ["Other"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "a #1 #2"}, "edge": {"12": "y #2", "13": "y #2", "14": "y #2", "15": "y #2", "16": "y #2", "17": "y #2", "18": "y #2"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "y #2", "37": "y #2", "38": "y #2", "39": "y #2", "40": "y #2", "41": "y #2", "42": "y #2", "43": "y #2", "44": "y #2", "45": "y #2", "46": "y #2", "47": "y #2", "48": "y #2", "49": "y #2", "50": "y #2", "51": "y #2", "52": "y #2", "53": "y #2 #4", "54": "y #2 #4", "55": "y #2 #4", "56": "y #2 #4", "57": "y #2 #4", "58": "y #2 #4", "59": "y #2 #4", "60": "y #2 #4", "61": "y #2 #4"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "y #2", "42": "y #2", "43": "y #2", "44": "y #2", "45": "y #2", "46": "y #2", "47": "y #2", "48": "y #2", "49": "y #2", "50": "y #2", "51": "y #2 #4", "52": "y #2 #4", "53": "y #2 #4", "54": "y #2 #4", "55": "y #2 #4", "56": "y #2 #4", "57": "y #2 #4", "58": "y #2 #4", "59": "y #2 #4", "60": "y #2 #4", "61": "y #2 #4", "62": "y #2 #4", "63": "y #2 #4", "64": "y #2 #4", "65": "y #2 #4", "66": "y #2 #4", "67": "y #2 #4", "68": "y #2 #4", "69": "y #2 #4"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "n", "8": "n", "9": "a #2 #3", "9.1": "a #2 #3", "10": "a #2 #3", "10.1": "a #2 #3", "11": "y #2", "11.1": "y #2", "TP": "y #2"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "y #2", "29": "y #2", "30": "y #2", "31": "y #2", "32": "y #2", "33": "y #2", "34": "y #2", "35": "y #2", "36": "y #2", "37": "y #2", "38": "y #2 #4", "39": "y #2 #4", "40": "y #2 #4", "41": "y #2 #4", "42": "y #2 #4", "43": "y #2 #4", "44": "y #2 #4", "45": "y #2 #4", "46": "y #2 #4", "47": "y #2 #4", "48": "y #2 #4", "49": "y #2 #4", "50": "y #2 #4", "51": "y #2 #4", "52": "y #2 #4"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "n", "8.1-8.4": "n", "9.0-9.2": "y #2", "9.3": "y #2", "10.0-10.2": "y #2", "10.3": "y #2", "11.0-11.2": "y #2", "11.3": "y #2"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "62": "y #2"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "y #2"}, "and_chr": {"66": "y #2 #4"}, "and_ff": {"57": "y #2"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.8": "n"}, "samsung": {"4": "y #2", "5": "y #2 #4", "6.2": "y #2 #4"}, "and_qq": {"1.2": "y #2 #4"}, "baidu": {"7.12": "y #2"}}, "notes": "See also support for [the SPDY protocol](https://caniuse.com/#feat=spdy), precursor of HTTP2.", "notes_by_num": {"1": "Partial support in IE11 refers to being limited to Windows 10.", "2": "Only supports HTTP2 over TLS (https)", "3": "Partial support in Safari refers to being limited to OSX 10.11+", "4": "Only supports HTTP2 if servers support protocol negotiation via ALPN"}, "usage_perc_y": 81.16, "usage_perc_a": 3.37, "ucprefix": false, "parent": "", "keywords": "", "ie_id": "http2", "chrome_id": "5152586365665280", "firefox_id": "http2", "webkit_id": "", "shown": true}