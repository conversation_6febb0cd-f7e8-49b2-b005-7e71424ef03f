{"title": "matches() DOM method", "description": "Method of testing whether or not a DOM element matches a given selector. Formerly known (and largely supported with prefix) as matchesSelector.", "spec": "https://dom.spec.whatwg.org/#dom-element-matches", "status": "ls", "links": [{"url": "https://developer.mozilla.org/en/docs/Web/API/Element/matches", "title": "MDN Web Docs - Element matches"}, {"url": "https://www.webplatform.org/docs/dom/HTMLElement/matches", "title": "WebPlatform Docs"}, {"url": "https://wpdev.uservoice.com/forums/257854-microsoft-edge-developer/suggestions/6524858-unprefix-and-update-msmatchesselector-to-matches", "title": "Microsoft Edge feature request on UserVoice"}], "bugs": [], "categories": ["DOM", "JS API"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "a x", "10": "a x", "11": "a x"}, "edge": {"12": "a x", "13": "a x", "14": "a x", "15": "y", "16": "y", "17": "y", "18": "y"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "a x", "4": "a x", "5": "a x", "6": "a x", "7": "a x", "8": "a x", "9": "a x", "10": "a x", "11": "a x", "12": "a x", "13": "a x", "14": "a x", "15": "a x", "16": "a x", "17": "a x", "18": "a x", "19": "a x", "20": "a x", "21": "a x", "22": "a x", "23": "a x", "24": "a x", "25": "a x", "26": "a x", "27": "a x", "28": "a x", "29": "a x", "30": "a x", "31": "a x", "32": "a x", "33": "a x", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y"}, "chrome": {"4": "a x", "5": "a x", "6": "a x", "7": "a x", "8": "a x", "9": "a x", "10": "a x", "11": "a x", "12": "a x", "13": "a x", "14": "a x", "15": "a x", "16": "a x", "17": "a x", "18": "a x", "19": "a x", "20": "a x", "21": "a x", "22": "a x", "23": "a x", "24": "a x", "25": "a x", "26": "a x", "27": "a x", "28": "a x", "29": "a x", "30": "a x", "31": "a x", "32": "a x", "33": "a x", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y", "63": "y", "64": "y", "65": "y", "66": "y", "67": "y", "68": "y", "69": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "a x", "5.1": "a x", "6": "a x", "6.1": "a x", "7": "a x", "7.1": "y", "8": "y", "9": "y", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "11.1": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "a x", "11.6": "a x", "12": "a x", "12.1": "a x", "15": "a x", "16": "a x", "17": "a x", "18": "a x", "19": "a x", "20": "a x", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y"}, "ios_saf": {"3.2": "n", "4.0-4.1": "a x", "4.2-4.3": "a x", "5.0-5.1": "a x", "6.0-6.1": "a x", "7.0-7.1": "a x", "8": "y", "8.1-8.4": "y", "9.0-9.2": "y", "9.3": "y", "10.0-10.2": "y", "10.3": "y", "11.0-11.2": "y", "11.3": "y"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "a x", "2.3": "a x", "3": "a x", "4": "a x", "4.1": "a x", "4.2-4.3": "a x", "4.4": "a x", "4.4.3-4.4.4": "a x", "62": "y"}, "bb": {"7": "a x", "10": "a x"}, "op_mob": {"10": "n", "11": "n", "11.1": "a x", "11.5": "a x", "12": "a x", "12.1": "a x", "37": "y"}, "and_chr": {"66": "y"}, "and_ff": {"57": "y"}, "ie_mob": {"10": "a x", "11": "a x"}, "and_uc": {"11.8": "y"}, "samsung": {"4": "a x", "5": "y", "6.2": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "Partial support refers to supporting the older specification's \"matchesSelector\" name rather than just \"matches\".", "notes_by_num": {}, "usage_perc_y": 88.52, "usage_perc_a": 6.69, "ucprefix": false, "parent": "", "keywords": " matchesSelector", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}