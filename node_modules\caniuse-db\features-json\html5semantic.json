{"title": "HTML5 semantic elements", "description": "HTML5 offers some new elements, primarily for semantic purposes. The elements include: `section`, `article`, `aside`, `header`, `footer`, `nav`, `figure`, `figcaption`, `time`, `mark` & `main`.", "spec": "https://html.spec.whatwg.org/multipage/semantics.html#sections", "status": "ls", "links": [{"url": "https://blog.whatwg.org/supporting-new-elements-in-ie", "title": "Workaround for IE"}, {"url": "https://blog.whatwg.org/styling-ie-noscript", "title": "Alternate workaround"}, {"url": "http://oli.jp/2009/html5-structure3/", "title": "Article on structural elements"}, {"url": "https://raw.github.com/phiggins42/has.js/master/detect/dom.js#dom-html5-elements", "title": "has.js test"}, {"url": "https://www.chromestatus.com/feature/5633937149788160", "title": "Chrome Platform Status: `<time>` element"}], "bugs": [{"description": "While the `time` and `data` elements can be used and work fine in all browsers, currently only Firefox and Edge 14+ recognize them officially as `HTMLTimeElement` and `HTMLDataElement` objects."}], "categories": ["HTML5"], "stats": {"ie": {"5.5": "n", "6": "p", "7": "p", "8": "p", "9": "a #2", "10": "a #2", "11": "a #2"}, "edge": {"12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y"}, "firefox": {"2": "n", "3": "a #1", "3.5": "a #1", "3.6": "a #1", "4": "a #2", "5": "a #2", "6": "a #2", "7": "a #2", "8": "a #2", "9": "a #2", "10": "a #2", "11": "a #2", "12": "a #2", "13": "a #2", "14": "a #2", "15": "a #2", "16": "a #2", "17": "a #2", "18": "a #2", "19": "a #2", "20": "a #2", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y"}, "chrome": {"4": "a #1", "5": "a #1", "6": "a #2", "7": "a #2", "8": "a #2", "9": "a #2", "10": "a #2", "11": "a #2", "12": "a #2", "13": "a #2", "14": "a #2", "15": "a #2", "16": "a #2", "17": "a #2", "18": "a #2", "19": "a #2", "20": "a #2", "21": "a #2", "22": "a #2", "23": "a #2", "24": "a #2", "25": "a #2", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y", "63": "y", "64": "y", "65": "y", "66": "y", "67": "y", "68": "y", "69": "y"}, "safari": {"3.1": "a #1", "3.2": "a #1", "4": "a #1", "5": "a #2", "5.1": "a #2", "6": "a #2", "6.1": "y", "7": "y", "7.1": "y", "8": "y", "9": "y", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "11.1": "y", "TP": "y"}, "opera": {"9": "a #1", "9.5-9.6": "a #1", "10.0-10.1": "a #1", "10.5": "a #1", "10.6": "a #1", "11": "a #1", "11.1": "a #2", "11.5": "a #2", "11.6": "a #2", "12": "a #2", "12.1": "a #2", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y"}, "ios_saf": {"3.2": "a #1", "4.0-4.1": "a #2", "4.2-4.3": "a #2", "5.0-5.1": "a #2", "6.0-6.1": "a #2", "7.0-7.1": "y", "8": "y", "8.1-8.4": "y", "9.0-9.2": "y", "9.3": "y", "10.0-10.2": "y", "10.3": "y", "11.0-11.2": "y", "11.3": "y"}, "op_mini": {"all": "a #1"}, "android": {"2.1": "a #1", "2.2": "a #2", "2.3": "a #2", "3": "a #2", "4": "a #2", "4.1": "a #2", "4.2-4.3": "a #2", "4.4": "y", "4.4.3-4.4.4": "y", "62": "y"}, "bb": {"7": "a #2", "10": "a #2"}, "op_mob": {"10": "a #1", "11": "a #2", "11.1": "a #2", "11.5": "a #2", "12": "a #2", "12.1": "a #2", "37": "y"}, "and_chr": {"66": "y"}, "and_ff": {"57": "y"}, "ie_mob": {"10": "a #2", "11": "a #2"}, "and_uc": {"11.8": "y"}, "samsung": {"4": "y", "5": "y", "6.2": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "", "notes_by_num": {"1": "Partial support refers to missing the default styling, as technically the elements are considered \"[unknown](https://developer.mozilla.org/en-US/docs/Web/API/HTMLUnknownElement)\". This is easily taken care of by manually setting the default `display` value for each tag.", "2": "Partial support refers to only the `<main>` element (added later to the spec) being \"unknown\", though it can still be used and styled."}, "usage_perc_y": 91.2, "usage_perc_a": 6.74, "ucprefix": false, "parent": "", "keywords": "", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}