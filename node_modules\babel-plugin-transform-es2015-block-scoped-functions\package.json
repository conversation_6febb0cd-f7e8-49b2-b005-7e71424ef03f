{"name": "babel-plugin-transform-es2015-block-scoped-functions", "version": "6.22.0", "description": "Babel plugin to ensure function declarations at the block level are block scoped", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-es2015-block-scoped-functions", "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"babel-runtime": "^6.22.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.22.0"}}