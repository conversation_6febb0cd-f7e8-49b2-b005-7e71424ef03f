{"title": "autocomplete attribute: on & off values", "description": "The `autocomplete` attribute for `input` elements indicates to the browser whether a value should or should not be autofilled when appropriate.", "spec": "https://html.spec.whatwg.org/multipage/forms.html#autofill", "status": "ls", "links": [{"url": "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#attr-autocomplete", "title": "MDN Web Docs - autocomplete attribute"}], "bugs": [], "categories": ["DOM"], "stats": {"ie": {"5.5": "y", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "a #1"}, "edge": {"12": "a #1", "13": "a #1", "14": "a #1", "15": "a #1", "16": "a #1", "17": "a #1", "18": "a #1"}, "firefox": {"2": "y", "3": "y", "3.5": "y", "3.6": "y", "4": "y", "5": "y", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "a #3", "31": "a #3", "32": "a #3", "33": "a #3", "34": "a #3", "35": "a #3", "36": "a #3", "37": "a #3", "38": "a #3", "39": "a #3", "40": "a #3", "41": "a #3", "42": "a #3", "43": "a #3", "44": "a #3", "45": "a #3", "46": "a #3", "47": "a #3", "48": "a #3", "49": "a #3", "50": "a #3", "51": "a #3", "52": "a #3", "53": "a #3", "54": "a #3", "55": "a #3", "56": "a #3", "57": "a #3", "58": "a #3", "59": "a #3", "60": "a #3", "61": "a #3"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "a #1", "28": "a #1", "29": "a #1", "30": "a #1", "31": "a #1", "32": "a #1", "33": "a #1", "34": "a #1", "35": "a #1", "36": "a #1", "37": "a #1", "38": "a #1", "39": "a #1", "40": "a #1", "41": "a #2", "42": "a #2", "43": "a #2", "44": "a #2", "45": "a #2", "46": "a #2", "47": "a #2", "48": "a #2", "49": "a #2", "50": "a #2", "51": "a #2", "52": "a #2", "53": "a #2", "54": "a #2", "55": "a #2", "56": "a #2", "57": "a #2", "58": "a #2", "59": "a #2", "60": "a #2", "61": "a #2", "62": "a #2", "63": "a #2", "64": "a #2", "65": "a #2", "66": "a #2", "67": "a #2", "68": "a #2", "69": "a #2"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "y", "6": "y", "6.1": "y", "7": "a #5", "7.1": "a #5", "8": "a #5", "9": "a #5", "9.1": "a #5", "10": "a #5", "10.1": "a #5", "11": "a #5", "11.1": "a #5", "TP": "a #5"}, "opera": {"9": "y", "9.5-9.6": "y", "10.0-10.1": "y", "10.5": "y", "10.6": "y", "11": "y", "11.1": "y", "11.5": "y", "11.6": "y", "12": "y", "12.1": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "y #4", "6.0-6.1": "y #4", "7.0-7.1": "y #4", "8": "y #4", "8.1-8.4": "y #4", "9.0-9.2": "y #4", "9.3": "y #4", "10.0-10.2": "y #4", "10.3": "y #4", "11.0-11.2": "y #4", "11.3": "y #4"}, "op_mini": {"all": "y #4"}, "android": {"2.1": "y", "2.2": "y", "2.3": "y", "3": "y", "4": "y", "4.1": "y", "4.2-4.3": "y", "4.4": "y", "4.4.3-4.4.4": "y", "62": "y"}, "bb": {"7": "y", "10": "y"}, "op_mob": {"10": "y", "11": "y", "11.1": "y", "11.5": "y", "12": "y", "12.1": "y", "37": "y"}, "and_chr": {"66": "y"}, "and_ff": {"57": "y"}, "ie_mob": {"10": "a #5", "11": "a #5"}, "and_uc": {"11.8": "y #4"}, "samsung": {"4": "y", "5": "y", "6.2": "y"}, "and_qq": {"1.2": "a #2"}, "baidu": {"7.12": "y"}}, "notes": "This support information does not include support for other `autocomplete` values.\r\n\r\nAs described in detail below, many modern browsers ignore the `off` value on certain fields in certain cases intentionally in order to give the user more control over autofilling fields. One example is the use of password managers.", "notes_by_num": {"1": "Partial support refers to ignoring the `off` value for password fields. [see related blog post](http://blogs.msdn.com/b/ieinternals/archive/2009/09/10/troubleshooting-stored-login-problems-in-ie.aspx)", "2": "Partial support in Chrome refers to the browser intentionally ignoring `autocomplete=\"off\"` when the user uses the browser's autofill functionality. [see bug](https://code.google.com/p/chromium/issues/detail?id=468153#c29)", "3": "Partial support in Firefox refers to ignoring `autocomplete=\"off\"` for login forms. [see bug](https://bugzilla.mozilla.org/show_bug.cgi?id=956906)", "4": "Browser does not display previously submitted values as options with `on` value.", "5": "Safari ignores the `off` value for [username, email and password fields](https://stackoverflow.com/questions/22661977/disabling-safari-autofill-on-usernames-and-passwords)"}, "usage_perc_y": 57.69, "usage_perc_a": 40.37, "ucprefix": false, "parent": "", "keywords": "", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}