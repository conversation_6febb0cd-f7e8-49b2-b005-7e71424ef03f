{"title": "CSS Masks", "description": "Method of displaying part of an element, using a selected image as a mask", "spec": "https://www.w3.org/TR/css-masking-1/", "status": "cr", "links": [{"url": "https://www.webplatform.org/docs/css/properties/mask", "title": "WebPlatform Docs"}, {"url": "https://www.html5rocks.com/en/tutorials/masking/adobe/", "title": "HTML5 Rocks article"}, {"url": "http://thenittygritty.co/css-masking", "title": "Detailed blog post"}, {"url": "https://bugzilla.mozilla.org/show_bug.cgi?id=1224422", "title": "Firefox implementation bug"}, {"url": "http://lab.iamvdo.me/css-svg-masks", "title": "Visual test cases"}], "bugs": [], "categories": ["CSS"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "a #4"}, "firefox": {"2": "n", "3": "n", "3.5": "a #2", "3.6": "a #2", "4": "a #2", "5": "a #2", "6": "a #2", "7": "a #2", "8": "a #2", "9": "a #2", "10": "a #2", "11": "a #2", "12": "a #2", "13": "a #2", "14": "a #2", "15": "a #2", "16": "a #2", "17": "a #2", "18": "a #2", "19": "a #2", "20": "a #2", "21": "a #2", "22": "a #2", "23": "a #2", "24": "a #2", "25": "a #2", "26": "a #2", "27": "a #2", "28": "a #2", "29": "a #2", "30": "a #2", "31": "a #2", "32": "a #2", "33": "a #2", "34": "a #2", "35": "a #2", "36": "a #2", "37": "a #2", "38": "a #2", "39": "a #2", "40": "a #2", "41": "a #2", "42": "a #2", "43": "a #2", "44": "a #2", "45": "a #2", "46": "a #2", "47": "a #2", "48": "a #2", "49": "a #2", "50": "a #2", "51": "a #2", "52": "a #2", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y"}, "chrome": {"4": "a x #1", "5": "a x #1", "6": "a x #1", "7": "a x #1", "8": "a x #1", "9": "a x #1", "10": "a x #1", "11": "a x #1", "12": "a x #1", "13": "a x #1", "14": "a x #1", "15": "a x #1", "16": "a x #1", "17": "a x #1", "18": "a x #1", "19": "a x #1", "20": "a x #1", "21": "a x #1", "22": "a x #1", "23": "a x #1", "24": "a x #1", "25": "a x #1", "26": "a x #1", "27": "a x #1", "28": "a x #1", "29": "a x #1", "30": "a x #1", "31": "a x #1", "32": "a x #1", "33": "a x #1", "34": "a x #1", "35": "a x #1", "36": "a x #1", "37": "a x #1", "38": "a x #1", "39": "a x #1", "40": "a x #1", "41": "a x #1", "42": "a x #1", "43": "a x #1", "44": "a x #1", "45": "a x #1", "46": "a x #1", "47": "a x #1", "48": "a x #1", "49": "a x #1", "50": "a x #1", "51": "a x #1", "52": "a x #1", "53": "a x #1", "54": "a x #1", "55": "a x #1", "56": "a x #1", "57": "a x #1", "58": "a x #1", "59": "a x #1", "60": "a x #1", "61": "a x #1", "62": "a x #1", "63": "a x #1", "64": "a x #1", "65": "a x #1", "66": "a x #1", "67": "a x #1", "68": "a x #1", "69": "a x #1"}, "safari": {"3.1": "n", "3.2": "n", "4": "a x #1", "5": "a x #1", "5.1": "a x #1", "6": "a x #1", "6.1": "a x #1", "7": "a x #1", "7.1": "a x #1", "8": "a x #1", "9": "a x #1", "9.1": "a x #1", "10": "a x #1", "10.1": "a x #1", "11": "a x #1", "11.1": "a x #1", "TP": "a x #1"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "a x #1", "16": "a x #1", "17": "a x #1", "18": "a x #1", "19": "a x #1", "20": "a x #1", "21": "a x #1", "22": "a x #1", "23": "a x #1", "24": "a x #1", "25": "a x #1", "26": "a x #1", "27": "a x #1", "28": "a x #1", "29": "a x #1", "30": "a x #1", "31": "a x #1", "32": "a x #1", "33": "a x #1", "34": "a x #1", "35": "a x #1", "36": "a x #1", "37": "a x #1", "38": "a x #1", "39": "a x #1", "40": "a x #1", "41": "a x #1", "42": "a x #1", "43": "a x #1", "44": "a x #1", "45": "a x #1", "46": "a x #1", "47": "a x #1", "48": "a x #1", "49": "a x #1", "50": "a x #1", "51": "a x #1", "52": "a x #1"}, "ios_saf": {"3.2": "a x #1", "4.0-4.1": "a x #1", "4.2-4.3": "a x #1", "5.0-5.1": "a x #1", "6.0-6.1": "a x #1", "7.0-7.1": "a x #1", "8": "a x #1", "8.1-8.4": "a x #1", "9.0-9.2": "a x #1", "9.3": "a x #1", "10.0-10.2": "a x #1", "10.3": "a x #1", "11.0-11.2": "a x #1", "11.3": "a x #1"}, "op_mini": {"all": "n"}, "android": {"2.1": "a x #1 #3", "2.2": "a x #1 #3", "2.3": "a x #1 #3", "3": "a x #1 #3", "4": "a x #1 #3", "4.1": "a x #1 #3", "4.2-4.3": "a x #1 #3", "4.4": "a x #1", "4.4.3-4.4.4": "a x #1", "62": "a x #1"}, "bb": {"7": "a x #1", "10": "a x #1"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "a x #1"}, "and_chr": {"66": "a x #1"}, "and_ff": {"57": "y"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.8": "a x #1"}, "samsung": {"4": "a x #1", "5": "a x #1", "6.2": "a x #1"}, "and_qq": {"1.2": "a x #1"}, "baidu": {"7.12": "a x #1"}}, "notes": "", "notes_by_num": {"1": "Partial support in WebKit/Blink browsers refers to supporting the mask-image and mask-box-image properties, but lacking support for other parts of the spec.", "2": "Partial support in Firefox refers to only support for inline SVG mask elements i.e. mask: url(#foo).", "3": "Partial support refers to supporting the mask-box-image shorthand but not the longhand properties", "4": "Partial support refers to supporting mask-image, mask-size, mask-position, and the mask shorthand"}, "usage_perc_y": 4.19, "usage_perc_a": 85.87, "ucprefix": false, "parent": "", "keywords": "clip,clip-path,clip-rule,mask,mask-border,mask-clip,mask-image,mask-mode,mask-type,css masking", "ie_id": "masks", "chrome_id": "5381559662149632", "firefox_id": "", "webkit_id": "", "shown": true}