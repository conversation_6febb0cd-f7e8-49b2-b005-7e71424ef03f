{"name": "babel-plugin-transform-runtime", "version": "6.23.0", "description": "Externalise references to helpers and builtins, automatically polyfilling your code without polluting globals", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-runtime", "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"babel-runtime": "^6.22.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.22.0"}}