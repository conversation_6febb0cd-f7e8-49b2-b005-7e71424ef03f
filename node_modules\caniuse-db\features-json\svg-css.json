{"title": "SVG in CSS backgrounds", "description": "Method of using SVG images as CSS backgrounds", "spec": "https://www.w3.org/TR/css3-background/#background-image", "status": "cr", "links": [{"url": "https://www.sitepoint.com/a-farewell-to-css3-gradients/", "title": "Tutorial for advanced effects"}], "bugs": [{"description": "Opera messes background repeat when changing zoom level: [known issue in Opera's private bug tracker (CORE-33071)](https://stackoverflow.com/questions/15220910/svg-as-css-background-problems-with-zoom-level-in-opera#comment21458317_15220910)"}, {"description": "IE 10 Mobile does not always provide crisp SVG backgrounds, particularly when zooming in."}, {"description": "Android 2.x will not display the fallback background-image in addition to not displaying the SVG if it is implemented using multiple background-image properties on the same element. (e.g. PNG background-image fallback + SVG background-image)"}], "categories": ["CSS3", "SVG"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "y", "10": "y", "11": "y"}, "edge": {"12": "a #3", "13": "a #3", "14": "a #3", "15": "a #3", "16": "y", "17": "y", "18": "y"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "a #2", "5": "a #2", "6": "a #2", "7": "a #2", "8": "a #2", "9": "a #2", "10": "a #2", "11": "a #2", "12": "a #2", "13": "a #2", "14": "a #2", "15": "a #2", "16": "a #2", "17": "a #2", "18": "a #2", "19": "a #2", "20": "a #2", "21": "a #2", "22": "a #2", "23": "a #2", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y"}, "chrome": {"4": "a", "5": "y", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y", "63": "y", "64": "y", "65": "y", "66": "y", "67": "y", "68": "y", "69": "y"}, "safari": {"3.1": "n", "3.2": "a #1", "4": "a #1", "5": "y", "5.1": "y", "6": "y", "6.1": "y", "7": "y", "7.1": "y", "8": "y", "9": "y", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "11.1": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "y", "10.0-10.1": "y", "10.5": "y", "10.6": "y", "11": "y", "11.1": "y", "11.5": "y", "11.6": "y", "12": "y", "12.1": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y"}, "ios_saf": {"3.2": "a #1", "4.0-4.1": "a #1", "4.2-4.3": "y", "5.0-5.1": "y", "6.0-6.1": "y", "7.0-7.1": "y", "8": "y", "8.1-8.4": "y", "9.0-9.2": "y", "9.3": "y", "10.0-10.2": "y", "10.3": "y", "11.0-11.2": "y", "11.3": "y"}, "op_mini": {"all": "a #2"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "y", "4": "y", "4.1": "y", "4.2-4.3": "y", "4.4": "y", "4.4.3-4.4.4": "y", "62": "y"}, "bb": {"7": "y", "10": "y"}, "op_mob": {"10": "a #2", "11": "a #2", "11.1": "a #2", "11.5": "a #2", "12": "a #2", "12.1": "a #2", "37": "y"}, "and_chr": {"66": "y"}, "and_ff": {"57": "y"}, "ie_mob": {"10": "y", "11": "y"}, "and_uc": {"11.8": "y"}, "samsung": {"4": "y", "5": "y", "6.2": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "", "notes_by_num": {"1": "Partial support in iOS Safari and older Safari versions refers to failing to support tiling or the background-position property.", "2": "Partial support in older Firefox and Opera Mini/Mobile refers to SVG images being blurry when scaled.", "3": "Partial support in Edge 15 and older refers to a lack of support for SVG data URIs. [see bug](https://developer.microsoft.com/en-us/microsoft-edge/platform/issues/6274479/)"}, "usage_perc_y": 94.83, "usage_perc_a": 3.08, "ucprefix": false, "parent": "", "keywords": "svg-in-css,svgincss,css-svg", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}