{"name": "babel-helper-function-name", "version": "6.24.1", "description": "Helper function to change the property 'name' of every function", "repository": "https://github.com/babel/babel/tree/master/packages/babel-helper-function-name", "license": "MIT", "main": "lib/index.js", "dependencies": {"babel-runtime": "^6.22.0", "babel-types": "^6.24.1", "babel-traverse": "^6.24.1", "babel-helper-get-function-arity": "^6.24.1", "babel-template": "^6.24.1"}}