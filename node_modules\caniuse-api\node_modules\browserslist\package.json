{"name": "browserslist", "version": "1.7.7", "description": "Share browsers list between different front-end tools, like Autoprefixer, Stylelint and babel-env-preset", "keywords": ["caniuse", "browsers"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "repository": "ai/browserslist", "dependencies": {"caniuse-db": "^1.0.30000639", "electron-to-chromium": "^1.2.7"}, "bin": "./cli.js", "devDependencies": {"eslint": "^3.18.0", "eslint-config-postcss": "^2.0.2", "jest": "^19.0.2", "lint-staged": "^3.4.0", "pre-commit": "^1.1.3", "yaspeller-ci": "^0.3.0"}, "eslintConfig": {"extends": "eslint-config-postcss/es5", "env": {"jest": true}, "rules": {"consistent-return": "off", "no-process-exit": "off", "valid-jsdoc": "error"}}, "jest": {"coverageThreshold": {"global": {"statements": 100}}}, "scripts": {"lint-staged": "lint-staged", "test": "jest --coverage && eslint *.js test/*.js && yaspeller-ci *.md"}, "lint-staged": {"*.md": "yaspeller-ci", "*.js": "eslint"}, "pre-commit": ["lint-staged"]}