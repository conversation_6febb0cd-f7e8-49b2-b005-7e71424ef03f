{"title": "CSS Logical Properties", "description": "Use start/end properties that depend on LTR or RTL writing direction instead of left/right", "spec": "https://www.w3.org/TR/css-logical-1/", "status": "wd", "links": [{"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/-moz-margin-start", "title": "MDN Web Docs - CSS -moz-margin-start"}, {"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/-moz-padding-start", "title": "MDN Web Docs - CSS -moz-padding-start"}, {"url": "https://wpdev.uservoice.com/forums/257854-microsoft-edge-developer/suggestions/7438435-css-logical-properties", "title": "Microsoft Edge feature request on UserVoice"}], "bugs": [], "categories": ["CSS", "CSS3"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n"}, "firefox": {"2": "n", "3": "a x #1", "3.5": "a x #1", "3.6": "a x #1", "4": "a x #1", "5": "a x #1", "6": "a x #1", "7": "a x #1", "8": "a x #1", "9": "a x #1", "10": "a x #1", "11": "a x #1", "12": "a x #1", "13": "a x #1", "14": "a x #1", "15": "a x #1", "16": "a x #1", "17": "a x #1", "18": "a x #1", "19": "a x #1", "20": "a x #1", "21": "a x #1", "22": "a x #1", "23": "a x #1", "24": "a x #1", "25": "a x #1", "26": "a x #1", "27": "a x #1", "28": "a x #1", "29": "a x #1", "30": "a x #1", "31": "a x #1", "32": "a x #1", "33": "a x #1", "34": "a x #1", "35": "a x #1", "36": "a x #1", "37": "a x #1", "38": "a x #1", "39": "a x #1", "40": "a x #1", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y"}, "chrome": {"4": "a x #2", "5": "a x #2", "6": "a x #2", "7": "a x #2", "8": "a x #2", "9": "a x #2", "10": "a x #2", "11": "a x #2", "12": "a x #2", "13": "a x #2", "14": "a x #2", "15": "a x #2", "16": "a x #2", "17": "a x #2", "18": "a x #2", "19": "a x #2", "20": "a x #2", "21": "a x #2", "22": "a x #2", "23": "a x #2", "24": "a x #2", "25": "a x #2", "26": "a x #2", "27": "a x #2", "28": "a x #2", "29": "a x #2", "30": "a x #2", "31": "a x #2", "32": "a x #2", "33": "a x #2", "34": "a x #2", "35": "a x #2", "36": "a x #2", "37": "a x #2", "38": "a x #2", "39": "a x #2", "40": "a x #2", "41": "a x #2", "42": "a x #2", "43": "a x #2", "44": "a x #2", "45": "a x #2", "46": "a x #2", "47": "a x #2", "48": "a x #2", "49": "a x #2", "50": "a x #2", "51": "a x #2", "52": "a x #2", "53": "a x #2", "54": "a x #2", "55": "a x #2", "56": "a x #2", "57": "a x #2", "58": "a x #2", "59": "a x #2", "60": "a x #2", "61": "a x #2", "62": "a x #2", "63": "a x #2", "64": "a x #2", "65": "a x #2", "66": "a x #2", "67": "a x #2", "68": "a x #2", "69": "a x #2"}, "safari": {"3.1": "a x #2", "3.2": "a x #2", "4": "a x #2", "5": "a x #2", "5.1": "a x #2", "6": "a x #2", "6.1": "a x #2", "7": "a x #2", "7.1": "a x #2", "8": "a x #2", "9": "a x #2", "9.1": "a x #2", "10": "a x #2", "10.1": "a x #2", "11": "a x #2", "11.1": "a x #2", "TP": "a x #2"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "a x #2", "16": "a x #2", "17": "a x #2", "18": "a x #2", "19": "a x #2", "20": "a x #2", "21": "a x #2", "22": "a x #2", "23": "a x #2", "24": "a x #2", "25": "a x #2", "26": "a x #2", "27": "a x #2", "28": "a x #2", "29": "a x #2", "30": "a x #2", "31": "a x #2", "32": "a x #2", "33": "a x #2", "34": "a x #2", "35": "a x #2", "36": "a x #2", "37": "a x #2", "38": "a x #2", "39": "a x #2", "40": "a x #2", "41": "a x #2", "42": "a x #2", "43": "a x #2", "44": "a x #2", "45": "a x #2", "46": "a x #2", "47": "a x #2", "48": "a x #2", "49": "a x #2", "50": "a x #2", "51": "a x #2", "52": "a x #2"}, "ios_saf": {"3.2": "a x #2", "4.0-4.1": "a x #2", "4.2-4.3": "a x #2", "5.0-5.1": "a x #2", "6.0-6.1": "a x #2", "7.0-7.1": "a x #2", "8": "a x #2", "8.1-8.4": "a x #2", "9.0-9.2": "a x #2", "9.3": "a x #2", "10.0-10.2": "a x #2", "10.3": "a x #2", "11.0-11.2": "a x #2", "11.3": "a x #2"}, "op_mini": {"all": "n"}, "android": {"2.1": "a x #2", "2.2": "a x #2", "2.3": "a x #2", "3": "a x #2", "4": "a x #2", "4.1": "a x #2", "4.2-4.3": "a x #2", "4.4": "a x #2", "4.4.3-4.4.4": "a x #2", "62": "a x #2"}, "bb": {"7": "a x #2", "10": "a x #2"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "a x #2"}, "and_chr": {"66": "a x #2"}, "and_ff": {"57": "a x #1"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.8": "a x #2"}, "samsung": {"4": "a x #2", "5": "a x #2", "6.2": "a x #2"}, "and_qq": {"1.2": "a x #2"}, "baidu": {"7.12": "a x #2"}}, "notes": "", "notes_by_num": {"1": "Only supports the *-start, and *-end values for `margin`, `border` and `padding`, not the inline/block type values as defined in the spec.", "2": "Like #1 but also supports `*-before` and `*-end` for `*-block-start` and `*-block-end` properties as well as `start` and `end` values for `text-align`"}, "usage_perc_y": 4.86, "usage_perc_a": 85.21, "ucprefix": false, "parent": "", "keywords": "margin-start,margin-end,padding-start,padding-end,border-start,border-end,inline-start,inline-end,block-start,block-end,block-size,inline-size", "ie_id": "csslogicalpropertieslevel1", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}