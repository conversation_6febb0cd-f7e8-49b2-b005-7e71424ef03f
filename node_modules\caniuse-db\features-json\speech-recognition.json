{"title": "Speech Recognition API", "description": "Method to provide speech input in a web browser.", "spec": "https://w3c.github.io/speech-api/speechapi.html#speechreco-section", "status": "unoff", "links": [{"url": "http://updates.html5rocks.com/2013/01/Voice-Driven-Web-Apps-Introduction-to-the-Web-Speech-API", "title": "HTML5Rocks article"}, {"url": "https://www.sitepoint.com/introducing-web-speech-api/", "title": "SitePoint article"}, {"url": "http://aurelio.audero.it/demo/web-speech-api-demo.html", "title": "Demo"}, {"url": "http://zenorocha.github.io/voice-elements/#recognition-element", "title": "Advanced demo and resource"}], "bugs": [], "categories": ["JS API"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n d #2", "23": "n d #2", "24": "n d #2", "25": "n d #2", "26": "n d #2", "27": "n d #2", "28": "n d #2", "29": "n d #2", "30": "n d #2", "31": "n d #2", "32": "n d #2", "33": "n d #2", "34": "n d #2", "35": "n d #2", "36": "n d #2", "37": "n d #2", "38": "n d #2", "39": "n d #2", "40": "n d #2", "41": "n d #2", "42": "n d #2", "43": "n d #2", "44": "n d #2", "45": "n d #2", "46": "n d #2", "47": "n d #2", "48": "n d #2", "49": "n d #2", "50": "n d #2", "51": "n d #2", "52": "n d #2", "53": "n d #2", "54": "n d #2", "55": "n d #2", "56": "n d #2", "57": "n d #2", "58": "n d #2", "59": "n d #2", "60": "n d #2", "61": "n d #2"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "a x #1", "26": "a x #1", "27": "a x #1", "28": "a x #1", "29": "a x #1", "30": "a x #1", "31": "a x #1", "32": "a x #1", "33": "a x #1", "34": "a x #1", "35": "a x #1", "36": "a x #1", "37": "a x #1", "38": "a x #1", "39": "a x #1", "40": "a x #1", "41": "a x #1", "42": "a x #1", "43": "a x #1", "44": "a x #1", "45": "a x #1", "46": "a x #1", "47": "a x #1", "48": "a x #1", "49": "a x #1", "50": "a x #1", "51": "a x #1", "52": "a x #1", "53": "a x #1", "54": "a x #1", "55": "a x #1", "56": "a x #1", "57": "a x #1", "58": "a x #1", "59": "a x #1", "60": "a x #1", "61": "a x #1", "62": "a x #1", "63": "a x #1", "64": "a x #1", "65": "a x #1", "66": "a x #1", "67": "a x #1", "68": "a x #1", "69": "a x #1"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "n", "8": "n", "9": "n", "9.1": "n", "10": "n", "10.1": "n", "11": "n", "11.1": "n", "TP": "n"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "a x #1", "28": "a x #1", "29": "a x #1", "30": "a x #1", "31": "a x #1", "32": "a x #1", "33": "a x #1", "34": "a x #1", "35": "a x #1", "36": "a x #1", "37": "a x #1", "38": "a x #1", "39": "a x #1", "40": "a x #1", "41": "a x #1", "42": "a x #1", "43": "a x #1", "44": "a x #1", "45": "a x #1", "46": "a x #1", "47": "a x #1", "48": "a x #1", "49": "a x #1", "50": "a x #1", "51": "a x #1", "52": "a x #1"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "n", "8.1-8.4": "n", "9.0-9.2": "n", "9.3": "n", "10.0-10.2": "n", "10.3": "n", "11.0-11.2": "n", "11.3": "n"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "62": "n"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "n"}, "and_chr": {"66": "a x #1"}, "and_ff": {"57": "n"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.8": "n"}, "samsung": {"4": "a x #1", "5": "a x #1", "6.2": "a x #1"}, "and_qq": {"1.2": "a x #1"}, "baidu": {"7.12": "a x #1"}}, "notes": "", "notes_by_num": {"1": "Partial support refers to some attributes missing.", "2": "Firefox currently has a `media.webspeech.recognition.enable` flag in about:config for this, but actual support is waiting for permissions to be sorted out.", "3": "Reported to be in development for Samsung Internet for GearVR, due Q1/2017"}, "usage_perc_y": 0, "usage_perc_a": 62.56, "ucprefix": false, "parent": "", "keywords": "#web-speech,speechrecognition,ASR", "ie_id": "webspeechapispeechrecognition", "chrome_id": "5908775487668224", "firefox_id": "webspeech-recognition", "webkit_id": "", "shown": true}