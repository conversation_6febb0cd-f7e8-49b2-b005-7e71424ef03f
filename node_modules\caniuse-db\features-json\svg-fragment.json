{"title": "SVG fragment identifiers", "description": "Method of displaying only a part of an SVG image by defining a view ID or view box dimensions as the file's fragment identifier.", "spec": "https://www.w3.org/TR/SVG/linking.html#SVGFragmentIdentifiers", "status": "rec", "links": [{"url": "http://www.broken-links.com/2012/08/14/better-svg-sprites-with-fragment-identifiers/", "title": "Blog post"}, {"url": "https://bugs.webkit.org/show_bug.cgi?id=91791", "title": "WebKit support bug"}], "bugs": [{"description": "For the ID syntax, Chrome, Opera, and (older) Safari require the ID to be set on a `view` element."}, {"description": "Currently does not work without `view` element for CSS background images in [Chrome](https://code.google.com/p/chromium/issues/detail?id=128055) and [Safari](https://bugs.webkit.org/show_bug.cgi?id=91790) (fixed in latest WebKit in June 2015)"}, {"description": "For Blink/WebKit browsers: If a page already includes an SVG fragment referred to inside `<img>` on a page it may also work correctly for elements with a CSS `background-image` set with the same URL. Because of this, caniuse for a period of time considered these browsers to have full support."}], "categories": ["SVG"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "y", "10": "y", "11": "y"}, "edge": {"12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "a #1", "37": "a #1", "38": "a #1", "39": "a #1", "40": "a #1", "41": "a #1", "42": "a #1", "43": "a #1", "44": "a #1", "45": "a #1", "46": "a #1", "47": "a #1", "48": "a #1", "49": "a #1", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y", "63": "y", "64": "y", "65": "y", "66": "y", "67": "y", "68": "y", "69": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "a #1", "8": "a #1", "9": "n", "9.1": "n", "10": "n", "10.1": "n", "11": "n", "11.1": "y", "TP": "y"}, "opera": {"9": "u", "9.5-9.6": "u", "10.0-10.1": "a", "10.5": "a", "10.6": "a", "11": "a", "11.1": "a", "11.5": "a", "11.6": "a", "12": "a", "12.1": "y", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "a #1", "24": "a #1", "25": "a #1", "26": "a #1", "27": "a #1", "28": "a #1", "29": "a #1", "30": "a #1", "31": "a #1", "32": "a #1", "33": "a #1", "34": "a #1", "35": "a #1", "36": "a #1", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "a #1", "8.1-8.4": "a #1", "9.0-9.2": "n", "9.3": "n", "10.0-10.2": "n", "10.3": "n", "11.0-11.2": "n", "11.3": "y"}, "op_mini": {"all": "y"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "62": "y"}, "bb": {"7": "n", "10": "a #1"}, "op_mob": {"10": "a", "11": "a", "11.1": "a", "11.5": "a", "12": "a", "12.1": "y", "37": "y"}, "and_chr": {"66": "y"}, "and_ff": {"57": "y"}, "ie_mob": {"10": "y", "11": "y"}, "and_uc": {"11.8": "y"}, "samsung": {"4": "a #1", "5": "y", "6.2": "y"}, "and_qq": {"1.2": "a #1"}, "baidu": {"7.12": "a #1"}}, "notes": "", "notes_by_num": {"1": "Partial support refers to support inside `<img>` but not as CSS `background-image`"}, "usage_perc_y": 80.41, "usage_perc_a": 2.59, "ucprefix": false, "parent": "", "keywords": "fragments,sprite,svg,use", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}