{"title": "Element.getBoundingClientRect()", "description": "Method to get the size and position of an element's bounding box, relative to the viewport.", "spec": "https://www.w3.org/TR/cssom-view/#dom-element-getboundingclientrect", "status": "wd", "links": [{"url": "https://developer.mozilla.org/en-US/docs/Web/API/Element/getBoundingClientRect", "title": "MDN Web Docs - getBoundingClientRect"}, {"url": "https://msdn.microsoft.com/en-us/library/ms536433(VS.85).aspx", "title": "Microsoft Developer Network"}], "bugs": [{"description": "In IE<=11, calling getBoundingClientRect on an element outside of the DOM throws an unspecified error instead of returning a 0x0 DOMRect. See [IE bug #829392.](https://connect.microsoft.com/IE/feedback/details/829392/calling-getboundingclientrect-on-an-html-element-that-has-not-been-added-to-the-dom-causes-unspecified-error)"}, {"description": "Really old versions of Safari and Chrome gave incorrect results for SVG elements. See [Chromium issue #47998.](https://code.google.com/p/chromium/issues/detail?id=47998)"}, {"description": "Safari and Chrome give incorrect results for elements that have the nonstandard `zoom` CSS property applied to them. See [WebKit bug #77998.](https://bugs.webkit.org/show_bug.cgi?id=77998)"}], "categories": ["JS API"], "stats": {"ie": {"5.5": "a #1 #3", "6": "a #1 #3", "7": "a #1 #3", "8": "a #1 #3", "9": "y", "10": "y", "11": "y"}, "edge": {"12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y"}, "firefox": {"2": "n", "3": "a #1 #4", "3.5": "a #2 #4", "3.6": "a #2 #3 #4", "4": "a #2", "5": "a #2", "6": "a #2", "7": "a #2", "8": "a #2", "9": "a #2", "10": "a #2", "11": "a #2", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y"}, "chrome": {"4": "y", "5": "y", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y", "63": "y", "64": "y", "65": "y", "66": "y", "67": "y", "68": "y", "69": "y"}, "safari": {"3.1": "u", "3.2": "u", "4": "y", "5": "y", "5.1": "y", "6": "y", "6.1": "y", "7": "y", "7.1": "y", "8": "y", "9": "y", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "11.1": "y", "TP": "y"}, "opera": {"9": "u", "9.5-9.6": "u", "10.0-10.1": "a #1", "10.5": "a #1", "10.6": "y", "11": "y", "11.1": "y", "11.5": "y", "11.6": "y", "12": "y", "12.1": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y"}, "ios_saf": {"3.2": "u", "4.0-4.1": "y", "4.2-4.3": "y", "5.0-5.1": "y", "6.0-6.1": "y", "7.0-7.1": "y", "8": "y", "8.1-8.4": "y", "9.0-9.2": "y", "9.3": "y", "10.0-10.2": "y", "10.3": "y", "11.0-11.2": "y", "11.3": "y"}, "op_mini": {"all": "y"}, "android": {"2.1": "u", "2.2": "u", "2.3": "y", "3": "y", "4": "y", "4.1": "y", "4.2-4.3": "y", "4.4": "y", "4.4.3-4.4.4": "y", "62": "y"}, "bb": {"7": "y", "10": "y"}, "op_mob": {"10": "a #1", "11": "y", "11.1": "y", "11.5": "y", "12": "y", "12.1": "y", "37": "y"}, "and_chr": {"66": "y"}, "and_ff": {"57": "y"}, "ie_mob": {"10": "y", "11": "y"}, "and_uc": {"11.8": "y"}, "samsung": {"4": "y", "5": "y", "6.2": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "", "notes_by_num": {"1": "The returned object lacks `width` and `height` properties.", "2": "Returns incorrect values for elements which have had CSS `transform`s applied to them.", "3": "The returned object cannot have new properties added to it; it's not extensible.", "4": "Existing properties of the returned object are immutable."}, "usage_perc_y": 97.82, "usage_perc_a": 0.3, "ucprefix": false, "parent": "", "keywords": "getBoundingClientRect,bounding,client,rect,DOMRect,box,cssom", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}