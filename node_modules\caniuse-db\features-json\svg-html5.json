{"title": "Inline SVG in HTML5", "description": "Method of using SVG tags directly in HTML documents. Requires HTML5 parser.", "spec": "https://html.spec.whatwg.org/multipage/embedded-content.html#svg-0", "status": "ls", "links": [{"url": "https://hacks.mozilla.org/2010/05/firefox-4-the-html5-parser-inline-svg-speed-and-more/", "title": "Mozilla Hacks blog post"}, {"url": "http://samples.msdn.microsoft.com/ietestcenter/html5/svghtml_harness.htm?url=SVG_HTML_Elements_001", "title": "Test suite"}], "bugs": [{"description": "Inline SVG in HTML works in Android 4+, but is reported to have bugs when manipulating the elements using JS, e.g. setting className does not work. Using getAttribute('class') and setAttribute('class', 'foobar') works."}], "categories": ["HTML5", "SVG"], "stats": {"ie": {"5.5": "n", "6": "p", "7": "p", "8": "p", "9": "y #1", "10": "y #1", "11": "y #1"}, "edge": {"12": "y #1", "13": "y #1", "14": "y #1", "15": "y #1", "16": "y #1", "17": "y #1", "18": "y #1"}, "firefox": {"2": "p", "3": "p", "3.5": "p", "3.6": "p", "4": "y", "5": "y", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y"}, "chrome": {"4": "p", "5": "p", "6": "p", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y", "63": "y", "64": "y", "65": "y", "66": "y", "67": "y", "68": "y", "69": "y"}, "safari": {"3.1": "p", "3.2": "p", "4": "p", "5": "p", "5.1": "y #1", "6": "y #1", "6.1": "y #1", "7": "y #1", "7.1": "y #1", "8": "y #1", "9": "y", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "11.1": "y", "TP": "y"}, "opera": {"9": "p", "9.5-9.6": "p", "10.0-10.1": "p", "10.5": "p", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "y", "12": "y", "12.1": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y"}, "ios_saf": {"3.2": "p", "4.0-4.1": "p", "4.2-4.3": "p", "5.0-5.1": "y #1", "6.0-6.1": "y #1", "7.0-7.1": "y #1", "8": "y #1", "8.1-8.4": "y #1", "9.0-9.2": "y", "9.3": "y", "10.0-10.2": "y", "10.3": "y", "11.0-11.2": "y", "11.3": "y"}, "op_mini": {"all": "y"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "y #1", "4": "y #1", "4.1": "y #1", "4.2-4.3": "y #1", "4.4": "y", "4.4.3-4.4.4": "y", "62": "y"}, "bb": {"7": "y #1", "10": "y"}, "op_mob": {"10": "p", "11": "p", "11.1": "p", "11.5": "p", "12": "y", "12.1": "y", "37": "y"}, "and_chr": {"66": "y"}, "and_ff": {"57": "y"}, "ie_mob": {"10": "y #1", "11": "y #1"}, "and_uc": {"11.8": "y"}, "samsung": {"4": "y", "5": "y", "6.2": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "", "notes_by_num": {"1": "Does not support CSS transforms on SVG elements (transform attribute can be used instead)"}, "usage_perc_y": 97.83, "usage_perc_a": 0, "ucprefix": false, "parent": "", "keywords": "", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}