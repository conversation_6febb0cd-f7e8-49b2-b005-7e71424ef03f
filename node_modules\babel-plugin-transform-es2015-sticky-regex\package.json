{"name": "babel-plugin-transform-es2015-sticky-regex", "version": "6.24.1", "description": "Compile ES2015 sticky regex to an ES5 RegExp constructor", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-es2015-sticky-regex", "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"babel-helper-regex": "^6.24.1", "babel-types": "^6.24.1", "babel-runtime": "^6.22.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.24.1"}}