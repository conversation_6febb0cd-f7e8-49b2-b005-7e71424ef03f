{"title": "readonly attribute of input and textarea elements", "description": "Makes the form control non-editable. Unlike the `disabled` attribute, `readonly` form controls are still included in form submissions and the user can still select (but not edit) their value text.", "spec": "https://html.spec.whatwg.org/multipage/forms.html#attr-input-readonly", "status": "ls", "links": [{"url": "https://html.spec.whatwg.org/multipage/forms.html#attr-textarea-readonly", "title": "WHATWG HTML specification for the readonly attribute of the `<textarea>` element"}, {"url": "https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Input#attr-readonly", "title": "MDN Web Docs - readonly attribute"}], "bugs": [], "categories": ["DOM", "HTML5"], "stats": {"ie": {"5.5": "u", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y"}, "edge": {"12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y"}, "firefox": {"2": "u", "3": "u", "3.5": "u", "3.6": "u", "4": "y", "5": "y", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y"}, "chrome": {"4": "u", "5": "u", "6": "u", "7": "u", "8": "u", "9": "u", "10": "u", "11": "u", "12": "u", "13": "u", "14": "u", "15": "u", "16": "u", "17": "u", "18": "u", "19": "u", "20": "u", "21": "u", "22": "u", "23": "u", "24": "u", "25": "u", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y", "63": "y", "64": "y", "65": "y", "66": "y", "67": "y", "68": "y", "69": "y"}, "safari": {"3.1": "u", "3.2": "u", "4": "u", "5": "u", "5.1": "y", "6": "y", "6.1": "y", "7": "y", "7.1": "y", "8": "y", "9": "y", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "11.1": "y", "TP": "y"}, "opera": {"9": "u", "9.5-9.6": "u", "10.0-10.1": "a #1", "10.5": "a #1", "10.6": "a #1", "11": "a #1", "11.1": "a #1", "11.5": "a #1", "11.6": "a #1", "12": "a #1", "12.1": "a #1", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y"}, "ios_saf": {"3.2": "u", "4.0-4.1": "u", "4.2-4.3": "u", "5.0-5.1": "u", "6.0-6.1": "u", "7.0-7.1": "y", "8": "y", "8.1-8.4": "y", "9.0-9.2": "y", "9.3": "y", "10.0-10.2": "y", "10.3": "y", "11.0-11.2": "y", "11.3": "y"}, "op_mini": {"all": "y"}, "android": {"2.1": "u", "2.2": "u", "2.3": "y", "3": "y", "4": "y", "4.1": "y", "4.2-4.3": "y", "4.4": "y", "4.4.3-4.4.4": "y", "62": "y"}, "bb": {"7": "y", "10": "y"}, "op_mob": {"10": "a #1", "11": "a #1", "11.1": "a #1", "11.5": "a #1", "12": "a #1", "12.1": "a #1", "37": "y"}, "and_chr": {"66": "y"}, "and_ff": {"57": "y"}, "ie_mob": {"10": "y #2", "11": "y #2"}, "and_uc": {"11.8": "y"}, "samsung": {"4": "y", "5": "y", "6.2": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "", "notes_by_num": {"1": "Readonly inputs of type `datetime-local`, `date`, `month`, and `week` can still be edited by pressing the Up or Down arrow keys on the keyboard while the input is focused.", "2": "Text cannot be selected directly, but is possible by first selecting any text around the field. "}, "usage_perc_y": 97.78, "usage_perc_a": 0.1, "ucprefix": false, "parent": "", "keywords": "readonly,read,attribute,edit,editable", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}