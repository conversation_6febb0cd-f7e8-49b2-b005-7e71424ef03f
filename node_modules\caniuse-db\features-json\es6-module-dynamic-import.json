{"title": "JavaScript modules: dynamic import()", "description": "Loading JavaScript modules dynamically using the import() syntax", "spec": "https://github.com/tc39/proposal-dynamic-import/blob/master/HTML%20Integration.md", "status": "unoff", "links": [{"url": "https://tc39.github.io/proposal-dynamic-import/", "title": "Counterpart ECMAScript specification for import() syntax"}, {"url": "https://blog.hospodarets.com/native-ecmascript-modules-dynamic-import", "title": "Blog post: Native ECMAScript modules - dynamic import()"}], "bugs": [], "categories": ["JS"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n", "46": "n", "47": "n", "48": "n", "49": "n", "50": "n", "51": "n", "52": "n", "53": "n", "54": "n", "55": "n", "56": "n", "57": "n", "58": "n", "59": "n", "60": "n", "61": "n"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n", "46": "n", "47": "n", "48": "n", "49": "n", "50": "n", "51": "n", "52": "n", "53": "n", "54": "n", "55": "n", "56": "n", "57": "n", "58": "n", "59": "n", "60": "n", "61": "n", "62": "n", "63": "n", "64": "n", "65": "n", "66": "n", "67": "n", "68": "n", "69": "n"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "n", "8": "n", "9": "n", "9.1": "n", "10": "n", "10.1": "n #1", "11": "n #1", "11.1": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n", "46": "n", "47": "n", "48": "n", "49": "n", "50": "n", "51": "n", "52": "n"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "n", "8.1-8.4": "n", "9.0-9.2": "n", "9.3": "n", "10.0-10.2": "n", "10.3": "n #1", "11.0-11.2": "u", "11.3": "u"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "62": "n"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "n"}, "and_chr": {"66": "n"}, "and_ff": {"57": "n"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.8": "n"}, "samsung": {"4": "n", "5": "n", "6.2": "n"}, "and_qq": {"1.2": "n"}, "baidu": {"7.12": "n"}}, "notes": "", "notes_by_num": {"1": "A [polyfill is available](https://gist.github.com/samthor/64b114e4a4f539915a95b91ffd340acc) for Safari 10.1/iOS Safari 10.3"}, "usage_perc_y": 0.02, "usage_perc_a": 0, "ucprefix": false, "parent": "es6-module", "keywords": "es6,javascript,module,import,export", "ie_id": "", "chrome_id": "5684934484164608", "firefox_id": "", "webkit_id": "", "shown": false}