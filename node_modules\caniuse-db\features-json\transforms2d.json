{"title": "CSS3 2D Transforms", "description": "Method of transforming an element including rotating, scaling, etc. Includes support for `transform` as well as `transform-origin` properties.", "spec": "https://www.w3.org/TR/css3-2d-transforms/", "status": "wd", "links": [{"url": "http://www.westciv.com/tools/transforms/", "title": "Live editor"}, {"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/transform", "title": "MDN Web Docs - CSS transform"}, {"url": "http://www.webresourcesdepot.com/cross-browser-css-transforms-csssandpaper/", "title": "Workaround script for IE"}, {"url": "http://www.useragentman.com/IETransformsTranslator/", "title": "Converter for IE"}, {"url": "https://raw.github.com/phiggins42/has.js/master/detect/css.js#css-transform", "title": "has.js test"}, {"url": "https://www.webplatform.org/docs/css/transforms/transform", "title": "WebPlatform Docs"}, {"url": "https://developer.microsoft.com/en-us/microsoft-edge/platform/status/supportcsstransformsonsvg/", "title": "IE platform status (SVG)"}], "bugs": [{"description": "<PERSON><PERSON> transforms in Android 2.3 fails to scale element background images."}, {"description": "In IE9 the caret of a `textarea` disappears when you use translate."}, {"description": "Firefox 42 and below do not support [`transform-origin` on SVG elements](https://bugzilla.mozilla.org/show_bug.cgi?id=923193). "}], "categories": ["CSS3"], "stats": {"ie": {"5.5": "n", "6": "p", "7": "p", "8": "p", "9": "y x #1", "10": "y #1", "11": "y #1"}, "edge": {"12": "y #1", "13": "y #1", "14": "y #1", "15": "y #1", "16": "y #1", "17": "y #1", "18": "y #1"}, "firefox": {"2": "n", "3": "n", "3.5": "y x", "3.6": "y x", "4": "y x", "5": "y x", "6": "y x", "7": "y x", "8": "y x", "9": "y x", "10": "y x", "11": "y x", "12": "y x", "13": "y x", "14": "y x", "15": "y x", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y"}, "chrome": {"4": "y x", "5": "y x", "6": "y x", "7": "y x", "8": "y x", "9": "y x", "10": "y x", "11": "y x", "12": "y x", "13": "y x", "14": "y x", "15": "y x", "16": "y x", "17": "y x", "18": "y x", "19": "y x", "20": "y x", "21": "y x", "22": "y x", "23": "y x", "24": "y x", "25": "y x", "26": "y x", "27": "y x", "28": "y x", "29": "y x", "30": "y x", "31": "y x", "32": "y x", "33": "y x", "34": "y x", "35": "y x", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y", "63": "y", "64": "y", "65": "y", "66": "y", "67": "y", "68": "y", "69": "y"}, "safari": {"3.1": "y x", "3.2": "y x", "4": "y x", "5": "y x", "5.1": "y x", "6": "y x", "6.1": "y x", "7": "y x", "7.1": "y x", "8": "y x", "9": "y", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "11.1": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "y x", "10.6": "y x", "11": "y x", "11.1": "y x", "11.5": "y x", "11.6": "y x", "12": "y x", "12.1": "y", "15": "y x", "16": "y x", "17": "y x", "18": "y x", "19": "y x", "20": "y x", "21": "y x", "22": "y x", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y"}, "ios_saf": {"3.2": "y x", "4.0-4.1": "y x", "4.2-4.3": "y x", "5.0-5.1": "y x", "6.0-6.1": "y x", "7.0-7.1": "y x", "8": "y x", "8.1-8.4": "y x", "9.0-9.2": "y", "9.3": "y", "10.0-10.2": "y", "10.3": "y", "11.0-11.2": "y", "11.3": "y"}, "op_mini": {"all": "n"}, "android": {"2.1": "y x", "2.2": "y x", "2.3": "y x", "3": "y x", "4": "y x", "4.1": "y x", "4.2-4.3": "y x", "4.4": "y x", "4.4.3-4.4.4": "y x", "62": "y"}, "bb": {"7": "y x", "10": "y x"}, "op_mob": {"10": "n", "11": "y", "11.1": "y", "11.5": "y", "12": "y", "12.1": "y", "37": "y"}, "and_chr": {"66": "y"}, "and_ff": {"57": "y"}, "ie_mob": {"10": "y", "11": "y"}, "and_uc": {"11.8": "y"}, "samsung": {"4": "y", "5": "y", "6.2": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "The scale transform can be emulated in IE < 9 using Microsoft's \"zoom\" extension, others are (not easily) possible using the MS Matrix filter", "notes_by_num": {"1": "Does not support CSS transforms on SVG elements (transform attribute can be used instead)"}, "usage_perc_y": 95.26, "usage_perc_a": 0, "ucprefix": false, "parent": "", "keywords": "transformation,translate,translatex,translatey,translatez,transform3d,rotation,rotate,scale,skew,css-transforms,transform-origin,transform:rotate,transform:scale,transform:skew", "ie_id": "transforms", "chrome_id": "6437640580628480", "firefox_id": "", "webkit_id": "", "shown": true}