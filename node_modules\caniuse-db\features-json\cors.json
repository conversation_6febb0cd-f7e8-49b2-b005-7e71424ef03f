{"title": "Cross-Origin Resource Sharing", "description": "Method of performing XMLHttpRequests across domains", "spec": "https://fetch.spec.whatwg.org/#http-cors-protocol", "status": "ls", "links": [{"url": "https://hacks.mozilla.org/2009/07/cross-site-xmlhttprequest-with-cors/", "title": "Mozilla Hacks blog post"}, {"url": "http://msdn.microsoft.com/en-us/library/cc288060(VS.85).aspx", "title": "Alternative implementation by IE8"}, {"url": "https://dev.opera.com/articles/view/dom-access-control-using-cross-origin-resource-sharing/", "title": "DOM access using CORS"}, {"url": "https://raw.github.com/phiggins42/has.js/master/detect/features.js#native-cors-xhr", "title": "has.js test"}, {"url": "https://developer.mozilla.org/en-US/docs/Web/HTTP/Access_control_CORS", "title": "MDN Web Docs - Access control CORS"}], "bugs": [{"description": "IE10+ does not send cookies when withCredential=true ([IE Bug #759587](https://connect.microsoft.com/IE/feedback/details/759587/ie10-doesnt-support-cookies-on-cross-origin-xmlhttprequest-withcredentials-true)). A workaround is [to use a P3P policy](http://www.techrepublic.com/blog/software-engineer/craft-a-p3p-policy-to-make-ie-behave/)"}, {"description": "IE10+ does not make a CORS request if port is the only difference ([IE Bug #781303](http://connect.microsoft.com/IE/feedback/details/781303))"}, {"description": "Android and some old versions of WebKit (that may be found in various webview implementations) do not support Access-Control-Expose-Headers: https://code.google.com/p/android/issues/detail?id=56726"}, {"description": "IE11 does not appear to support CORS for images in the `canvas` element"}], "categories": ["JS API", "Security"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "a #2", "9": "a #2", "10": "a #1", "11": "y"}, "edge": {"12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y"}, "firefox": {"2": "n", "3": "n", "3.5": "y", "3.6": "y", "4": "y", "5": "y", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y"}, "chrome": {"4": "a #1", "5": "a #1", "6": "a #1", "7": "a #1", "8": "a #1", "9": "a #1", "10": "a #1", "11": "a #1", "12": "a #1", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y", "63": "y", "64": "y", "65": "y", "66": "y", "67": "y", "68": "y", "69": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "a #1 #3", "5": "a #1 #3", "5.1": "a #1 #3", "6": "y #3", "6.1": "y #3", "7": "y #3", "7.1": "y #3", "8": "y #3", "9": "y #3", "9.1": "y #3", "10": "y #3", "10.1": "y #3", "11": "y #3", "11.1": "y #3", "TP": "y #3"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "y", "12.1": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y"}, "ios_saf": {"3.2": "a #1 #3", "4.0-4.1": "a #1 #3", "4.2-4.3": "a #1 #3", "5.0-5.1": "a #1 #3", "6.0-6.1": "y #3", "7.0-7.1": "y #3", "8": "y #3", "8.1-8.4": "y #3", "9.0-9.2": "y #3", "9.3": "y #3", "10.0-10.2": "y #3", "10.3": "y #3", "11.0-11.2": "y #3", "11.3": "y #3"}, "op_mini": {"all": "n"}, "android": {"2.1": "a #1", "2.2": "a #1", "2.3": "a #1", "3": "a #1", "4": "a #1", "4.1": "a #1", "4.2-4.3": "a #1", "4.4": "y", "4.4.3-4.4.4": "y", "62": "y"}, "bb": {"7": "a #1", "10": "y"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "y", "12.1": "y", "37": "y"}, "and_chr": {"66": "y"}, "and_ff": {"57": "y"}, "ie_mob": {"10": "a #1", "11": "y"}, "and_uc": {"11.8": "y"}, "samsung": {"4": "y", "5": "y", "6.2": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "", "notes_by_num": {"1": "Does not support CORS for images in `<canvas>`", "2": "Supported somewhat in IE8 and IE9 using the XDomainRequest object (but has [limitations](http://blogs.msdn.com/b/ieinternals/archive/2010/05/13/xdomainrequest-restrictions-limitations-and-workarounds.aspx))", "3": "Does not support CORS for `<video>` in `<canvas>`: https://bugs.webkit.org/show_bug.cgi?id=135379"}, "usage_perc_y": 94.5, "usage_perc_a": 0.88, "ucprefix": false, "parent": "", "keywords": "", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}