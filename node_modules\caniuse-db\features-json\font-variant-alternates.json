{"title": "CSS font-variant-alternates", "description": "Controls the usage of alternate glyphs associated to alternative names defined in @font-feature-values for certain types of OpenType fonts.", "spec": "https://www.w3.org/TR/css-fonts-3/#propdef-font-variant-alternates", "status": "cr", "links": [{"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/font-variant-alternates", "title": "MDN Web Docs - font-variant-alternates"}], "bugs": [], "categories": ["CSS3"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n #1", "11": "n #1"}, "edge": {"12": "n #1", "13": "n #1", "14": "n #1", "15": "n #1", "16": "n #1", "17": "n #1", "18": "n #1"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n #1", "5": "n #1", "6": "n #1", "7": "n #1", "8": "n #1", "9": "n #1", "10": "n #1", "11": "n #1", "12": "n #1", "13": "n #1", "14": "n #1", "15": "n #1", "16": "n #1", "17": "n #1", "18": "n #1", "19": "n #1", "20": "n #1", "21": "n #1", "22": "n #1", "23": "n #1", "24": "n d #2", "25": "n d #2", "26": "n d #2", "27": "n d #2", "28": "n d #2", "29": "n d #2", "30": "n d #2", "31": "n d #2", "32": "n d #2", "33": "n d #2", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n #1", "17": "n #1", "18": "n #1", "19": "n #1", "20": "n #1", "21": "n #1", "22": "n #1", "23": "n #1", "24": "n #1", "25": "n #1", "26": "n #1", "27": "n #1", "28": "n #1", "29": "n #1", "30": "n #1", "31": "n #1", "32": "n #1", "33": "n #1", "34": "n #1", "35": "n #1", "36": "n #1", "37": "n #1", "38": "n #1", "39": "n #1", "40": "n #1", "41": "n #1", "42": "n #1", "43": "n #1", "44": "n #1", "45": "n #1", "46": "n #1", "47": "n #1", "48": "n #1", "49": "n #1", "50": "n #1", "51": "n #1", "52": "n #1", "53": "n #1", "54": "n #1", "55": "n #1", "56": "n #1", "57": "n #1", "58": "n #1", "59": "n #1", "60": "n #1", "61": "n #1", "62": "n #1", "63": "n #1", "64": "n #1", "65": "n #1", "66": "n #1", "67": "n #1", "68": "n #1", "69": "n #1"}, "safari": {"3.1": "n", "3.2": "n", "4": "n #1", "5": "n #1", "5.1": "n #1", "6": "n #1", "6.1": "n", "7": "n", "7.1": "n", "8": "n", "9": "n", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "11.1": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "n #1", "16": "n #1", "17": "n #1", "18": "n #1", "19": "n #1", "20": "n #1", "21": "n #1", "22": "n #1", "23": "n #1", "24": "n #1", "25": "n #1", "26": "n #1", "27": "n #1", "28": "n #1", "29": "n #1", "30": "n #1", "31": "n #1", "32": "n #1", "33": "n #1", "34": "n #1", "35": "n #1", "36": "n #1", "37": "n #1", "38": "n #1", "39": "n #1", "40": "n #1", "41": "n #1", "42": "n #1", "43": "n #1", "44": "n #1", "45": "n #1", "46": "n #1", "47": "n #1", "48": "n #1", "49": "n #1", "50": "n #1", "51": "n #1", "52": "n #1"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n #1", "4.2-4.3": "n #1", "5.0-5.1": "n #1", "6.0-6.1": "n #1", "7.0-7.1": "n", "8": "n", "8.1-8.4": "n", "9.0-9.2": "n", "9.3": "y", "10.0-10.2": "y", "10.3": "y", "11.0-11.2": "y", "11.3": "y"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n #1", "4.4.3-4.4.4": "n #1", "62": "n #1"}, "bb": {"7": "n", "10": "n #1"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "n #1"}, "and_chr": {"66": "n #1"}, "and_ff": {"57": "y"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.8": "n #1"}, "samsung": {"4": "n #1", "5": "n #1", "6.2": "n #1"}, "and_qq": {"1.2": "n #1"}, "baidu": {"7.12": "n #1"}}, "notes": "", "notes_by_num": {"1": "Low-level syntax available in [font-feature-settings](https://caniuse.com/#feat=font-feature) property equivalent to OpenType features: salt, ss01 through ss20, cv01 through cv99, swsh, cswh, ornm, nalt", "2": "Experimental support available by enabling the layout.css.font-features.enabled flag"}, "usage_perc_y": 17.85, "usage_perc_a": 0, "ucprefix": false, "parent": "", "keywords": "font-feature,font-feature-settings,font-variant", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}