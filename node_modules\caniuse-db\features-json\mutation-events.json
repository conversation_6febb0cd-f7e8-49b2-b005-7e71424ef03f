{"title": "Mutation events", "description": "Deprecated mechanism for listening to changes made to the DOM, replaced by Mutation Observers.", "spec": "https://www.w3.org/TR/DOM-Level-3-Events/#legacy-mutationevent-events", "status": "wd", "links": [{"url": "https://developer.mozilla.org/en-US/docs/Web/Guide/Events/Mutation_events", "title": "MDN Web Docs - Mutation events"}], "bugs": [], "categories": ["DOM"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "a #2", "10": "a #2", "11": "a #2"}, "edge": {"12": "a #2", "13": "a #2", "14": "a #2", "15": "a #2", "16": "a #2", "17": "a #2", "18": "a #2"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "a #2", "7": "a #2", "8": "a #2", "9": "a #2", "10": "a #2", "11": "a #2", "12": "a #2", "13": "a #2", "14": "a #2", "15": "a #2", "16": "a #2", "17": "a #2", "18": "a #2", "19": "a #2", "20": "a #2", "21": "a #2", "22": "a #2", "23": "a #2", "24": "a #2", "25": "a #2", "26": "a #2", "27": "a #2", "28": "a #2", "29": "a #2", "30": "a #2", "31": "a #2", "32": "a #2", "33": "a #2", "34": "a #2", "35": "a #2", "36": "a #2", "37": "a #2", "38": "a #2", "39": "a #2", "40": "a #2", "41": "a #2", "42": "a #2", "43": "a #2", "44": "a #2", "45": "a #2", "46": "a #2", "47": "a #2", "48": "a #2", "49": "a #2", "50": "a #2", "51": "a #2", "52": "a #2", "53": "a #2", "54": "a #2", "55": "a #2", "56": "a #2", "57": "a #2", "58": "a #2", "59": "a #2", "60": "a #2", "61": "a #2"}, "chrome": {"4": "u", "5": "u", "6": "u", "7": "u", "8": "u", "9": "u", "10": "u", "11": "u", "12": "u", "13": "u", "14": "u", "15": "a #1", "16": "a #1", "17": "a #1", "18": "a #1", "19": "a #1", "20": "a #1", "21": "a #1", "22": "a #1", "23": "a #1", "24": "a #1", "25": "a #1", "26": "a #1", "27": "a #1", "28": "a #1", "29": "a #1", "30": "a #1", "31": "a #1", "32": "a #1", "33": "a #1", "34": "a #1", "35": "a #1", "36": "a #1", "37": "a #1", "38": "a #1", "39": "a #1", "40": "a #1", "41": "a #1", "42": "a #1", "43": "a #1", "44": "a #1", "45": "a #1", "46": "a #1", "47": "a #1", "48": "a #1", "49": "a #1", "50": "a #1", "51": "a #1", "52": "a #1", "53": "a #1", "54": "a #1", "55": "a #1", "56": "a #1", "57": "a #1", "58": "a #1", "59": "a #1", "60": "a #1", "61": "a #1", "62": "a #1", "63": "a #1", "64": "a #1", "65": "a #1", "66": "a #1", "67": "a #1", "68": "a #1", "69": "a #1"}, "safari": {"3.1": "u", "3.2": "u", "4": "a #1", "5": "a #1", "5.1": "a #1", "6": "a #1", "6.1": "a #1", "7": "a #1", "7.1": "a #1", "8": "a #1", "9": "a #1", "9.1": "a #1", "10": "a #1", "10.1": "a #1", "11": "a #1", "11.1": "a #1", "TP": "a #1"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "u", "11.1": "u", "11.5": "u", "11.6": "y", "12": "y", "12.1": "y", "15": "a #1", "16": "a #1", "17": "a #1", "18": "a #1", "19": "a #1", "20": "a #1", "21": "a #1", "22": "a #1", "23": "a #1", "24": "a #1", "25": "a #1", "26": "a #1", "27": "a #1", "28": "a #1", "29": "a #1", "30": "a #1", "31": "a #1", "32": "a #1", "33": "a #1", "34": "a #1", "35": "a #1", "36": "a #1", "37": "a #1", "38": "a #1", "39": "a #1", "40": "a #1", "41": "a #1", "42": "a #1", "43": "a #1", "44": "a #1", "45": "a #1", "46": "a #1", "47": "a #1", "48": "a #1", "49": "a #1", "50": "a #1", "51": "a #1", "52": "a #1"}, "ios_saf": {"3.2": "u", "4.0-4.1": "u", "4.2-4.3": "a #1", "5.0-5.1": "a #1", "6.0-6.1": "a #1", "7.0-7.1": "a #1", "8": "a #1", "8.1-8.4": "a #1", "9.0-9.2": "a #1", "9.3": "a #1", "10.0-10.2": "a #1", "10.3": "a #1", "11.0-11.2": "a #1", "11.3": "a #1"}, "op_mini": {"all": "n"}, "android": {"2.1": "u", "2.2": "u", "2.3": "a #1", "3": "a #1", "4": "a #1", "4.1": "a #1", "4.2-4.3": "a #1", "4.4": "a #1", "4.4.3-4.4.4": "a #1", "62": "a #1"}, "bb": {"7": "a #1", "10": "a #1"}, "op_mob": {"10": "n", "11": "u", "11.1": "u", "11.5": "u", "12": "y", "12.1": "y", "37": "a #1"}, "and_chr": {"66": "a #1"}, "and_ff": {"57": "a #2"}, "ie_mob": {"10": "a #2", "11": "a #2"}, "and_uc": {"11.8": "a #1"}, "samsung": {"4": "a #1", "5": "a #1", "6.2": "a #1"}, "and_qq": {"1.2": "a #1"}, "baidu": {"7.12": "a #1"}}, "notes": "See also support for [Mutation Observer](https://caniuse.com/#feat=mutationobserver), which replaces mutation events and does not have the same performance drawbacks.", "notes_by_num": {"1": "Does not support `DOMAttrModified`", "2": "Does not support `DOMNodeInsertedIntoDocument` & `DOMNodeRemovedFromDocument`"}, "usage_perc_y": 0.06, "usage_perc_a": 95.06, "ucprefix": false, "parent": "", "keywords": "DOMAttrModified,DOMCharacterDataModified,DOMNodeInserted,DOMNodeInsertedIntoDocument,DOMNodeRemoved,DOMNodeRemovedFromDocument,DOMSubtreeModified", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}