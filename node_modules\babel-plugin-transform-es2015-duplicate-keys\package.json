{"name": "babel-plugin-transform-es2015-duplicate-keys", "version": "6.24.1", "description": "Compile objects with duplicate keys to valid strict ES5", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-es2015-duplicate-keys", "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"babel-runtime": "^6.22.0", "babel-types": "^6.24.1"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.24.1"}}