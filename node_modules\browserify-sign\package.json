{"name": "browserify-sign", "version": "4.0.4", "description": "adds node crypto signing for browsers", "bugs": {"url": "https://github.com/crypto-browserify/browserify-sign/issues"}, "license": "ISC", "files": ["browser", "index.js", "algos.js"], "main": "index.js", "repository": {"type": "git", "url": "https://github.com/crypto-browserify/browserify-sign.git"}, "scripts": {"coverage": "nyc npm run unit", "lint": "standard", "test": "npm run lint && npm run unit", "unit": "tape test/*.js"}, "dependencies": {"bn.js": "^4.1.1", "browserify-rsa": "^4.0.0", "create-hash": "^1.1.0", "create-hmac": "^1.1.2", "elliptic": "^6.0.0", "inherits": "^2.0.1", "parse-asn1": "^5.0.0"}, "devDependencies": {"nyc": "^6.1.1", "standard": "^6.0.8", "tape": "^4.5.1"}, "browser": "browser/index.js"}