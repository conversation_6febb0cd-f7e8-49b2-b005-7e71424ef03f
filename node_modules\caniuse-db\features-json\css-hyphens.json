{"title": "CSS Hyphenation", "description": "Method of controlling when words at the end of lines should be hyphenated using the \"hyphens\" property.", "spec": "https://www.w3.org/TR/css3-text/#hyphenation", "status": "wd", "links": [{"url": "https://developer.mozilla.org/en/CSS/hyphens", "title": "MDN Web Docs - CSS hyphens"}, {"url": "http://blog.fontdeck.com/post/9037028497/hyphens", "title": "Blog post"}, {"url": "https://www.webplatform.org/docs/css/properties/hyphens", "title": "WebPlatform Docs"}, {"url": "https://bugs.chromium.org/p/chromium/issues/detail?id=605840", "title": "Chrome bug for implementing hyphenation"}], "bugs": [], "categories": ["CSS3"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "y x", "11": "y x"}, "edge": {"12": "y x", "13": "y x", "14": "y x", "15": "y x", "16": "y x", "17": "y x", "18": "y x"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "y x", "7": "y x", "8": "y x", "9": "y x", "10": "y x", "11": "y x", "12": "y x", "13": "y x", "14": "y x", "15": "y x", "16": "y x", "17": "y x", "18": "y x", "19": "y x", "20": "y x", "21": "y x", "22": "y x", "23": "y x", "24": "y x", "25": "y x", "26": "y x", "27": "y x", "28": "y x", "29": "y x", "30": "y x", "31": "y x", "32": "y x", "33": "y x", "34": "y x", "35": "y x", "36": "y x", "37": "y x", "38": "y x", "39": "y x", "40": "y x", "41": "y x", "42": "y x", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n", "46": "n", "47": "n", "48": "n", "49": "n", "50": "n", "51": "n", "52": "n", "53": "n", "54": "n", "55": "a #1", "56": "a #1", "57": "a #1", "58": "a #1", "59": "a #1", "60": "a #1", "61": "a #1", "62": "a #1", "63": "a #1", "64": "a #1", "65": "a #1", "66": "a #1", "67": "a #1", "68": "a #1", "69": "a #1"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "y x", "6": "y x", "6.1": "y x", "7": "y x", "7.1": "y x", "8": "y x", "9": "y x", "9.1": "y x", "10": "y x", "10.1": "y x", "11": "y x", "11.1": "y x", "TP": "y x"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "a #1", "43": "a #1", "44": "a #1", "45": "a #1", "46": "a #1", "47": "a #1", "48": "a #1", "49": "a #1", "50": "a #1", "51": "a #1", "52": "a #1"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "y x", "5.0-5.1": "y x", "6.0-6.1": "y x", "7.0-7.1": "y x", "8": "y x", "8.1-8.4": "y x", "9.0-9.2": "y x", "9.3": "y x", "10.0-10.2": "y x", "10.3": "y x", "11.0-11.2": "y x", "11.3": "y x"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "62": "a #1"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "n"}, "and_chr": {"66": "a #1"}, "and_ff": {"57": "y"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.8": "a"}, "samsung": {"4": "n", "5": "a #1", "6.2": "y"}, "and_qq": {"1.2": "n"}, "baidu": {"7.12": "a #1"}}, "notes": "Chrome < 55 and Android 4.0 Browser support \"-webkit-hyphens: none\", but not the \"auto\" property. It is [advisable to set the @lang attribute](http://blog.adrianroselli.com/2015/01/on-use-of-lang-attribute.html) on the HTML element to enable hyphenation support and improve accessibility.", "notes_by_num": {"1": "Only supported on Android & Mac platforms (and only the \"auto\" value) for now. [See commit](https://crrev.com/ed7e106e0e48b3afb160a5bdbb37649e307d2b05) & related [bug](https://bugs.chromium.org/p/chromium/issues/detail?id=652964)."}, "usage_perc_y": 24.89, "usage_perc_a": 65.28, "ucprefix": false, "parent": "", "keywords": "hyphen,shy", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}