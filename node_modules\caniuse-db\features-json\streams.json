{"title": "Streams", "description": "Method of creating, composing, and consuming streams of data, that map efficiently to low-level I/O primitives, and allow easy composition with built-in backpressure and queuing.", "spec": "https://streams.spec.whatwg.org/", "status": "ls", "links": [{"url": "https://github.com/whatwg/streams", "title": "GitHub repository"}, {"url": "https://developer.mozilla.org/en/docs/Web/API/ReadableStream", "title": "ReadableStream on Mozilla Developer Network"}, {"url": "https://jakearchibald.com/2016/streams-ftw/", "title": "Blog article about streams"}], "bugs": [], "categories": ["JS API"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n #1"}, "edge": {"12": "u", "13": "u", "14": "a #2", "15": "a #2", "16": "a #4 #6", "17": "a #4 #6", "18": "a #4 #6"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n", "46": "n", "47": "n", "48": "n", "49": "n", "50": "n", "51": "n", "52": "n", "53": "n", "54": "n", "55": "n", "56": "n", "57": "n d #3 #5", "58": "n d #3 #5", "59": "n d #3 #5", "60": "n d #3 #5", "61": "n d #3 #5"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n", "46": "n", "47": "n", "48": "n", "49": "n", "50": "n", "51": "n", "52": "a #2", "53": "a #2", "54": "a #2", "55": "a #2", "56": "a #2", "57": "a #2", "58": "a #2", "59": "a #4", "60": "a #4", "61": "a #4", "62": "a #4", "63": "a #4", "64": "a #4", "65": "a #4", "66": "a #4", "67": "a #4", "68": "a #4", "69": "a #4"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "n", "8": "n", "9": "n", "9.1": "n", "10": "a #4 #5", "10.1": "a #4 #5", "11": "a #4 #5", "11.1": "a #4 #5", "TP": "a #4 #5"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "a #2", "40": "a #2", "41": "a #2", "42": "a #2", "43": "a #2", "44": "a #2", "45": "a #2", "46": "a #4", "47": "a #4", "48": "a #4", "49": "a #4", "50": "a #4", "51": "a #4", "52": "a #4"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "n", "8.1-8.4": "n", "9.0-9.2": "n", "9.3": "n", "10.0-10.2": "u", "10.3": "a #4", "11.0-11.2": "a #4", "11.3": "a #4"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "62": "a #2"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "a #4"}, "and_chr": {"66": "a #4"}, "and_ff": {"57": "n d #3 #5"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.8": "n"}, "samsung": {"4": "n", "5": "n", "6.2": "n"}, "and_qq": {"1.2": "n"}, "baidu": {"7.12": "n"}}, "notes": "", "notes_by_num": {"1": "IE implements a different API than the one from WHATWG.", "2": "Only basic read support.", "3": "Disabled by default behind the `javascript.options.streams` and `dom.streams.enabled` flags. ", "4": "No support for BYOB (\"bring your own buffer\") stream readers.", "5": "No support for `WritableStream`.", "6": "No support for the `pipeTo` or `pipeThrough` methods"}, "usage_perc_y": 0, "usage_perc_a": 71.17, "ucprefix": false, "parent": "", "keywords": "streams,writablestream,readablestream", "ie_id": "streamsapireadablestream,streamsapiwritablestream", "chrome_id": "6605041225957376", "firefox_id": "streams", "webkit_id": "specification-streams", "shown": true}