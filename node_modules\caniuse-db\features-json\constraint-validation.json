{"title": "Constraint Validation API", "description": "API for better control over form field validation. Includes support for `checkValidity()`, `setCustomValidity()`, `reportValidity()` and validation states.", "spec": "https://html.spec.whatwg.org/dev/form-control-infrastructure.html#the-constraint-validation-api", "status": "ls", "links": [{"url": "https://developer.mozilla.org/en-US/docs/Web/Guide/HTML/HTML5/Constraint_validation", "title": "MDN article on constraint validation"}, {"url": "https://wpdev.uservoice.com/forums/257854-microsoft-edge-developer/suggestions/14744163-support-htmlformelement-reportvalidity", "title": "MS Edge UserVoice request for reportValidity"}], "bugs": [], "categories": ["JS API"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "a #1 #2 #3", "11": "a #1 #2 #3"}, "edge": {"12": "a #1 #2 #3", "13": "a #1 #2 #3", "14": "a #1 #2", "15": "a #1 #2", "16": "a #1 #2", "17": "y", "18": "y"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "a #1 #2 #3", "5": "a #1 #2 #3", "6": "a #1 #2 #3", "7": "a #1 #2 #3", "8": "a #1 #2 #3", "9": "a #1 #2 #3", "10": "a #1 #2 #3", "11": "a #1 #2 #3", "12": "a #1 #2 #3", "13": "a #1 #2 #3", "14": "a #1 #2 #3", "15": "a #1 #2 #3", "16": "a #1 #2 #3", "17": "a #1 #2 #3", "18": "a #1 #2 #3", "19": "a #1 #2 #3", "20": "a #1 #2 #3", "21": "a #1 #2 #3", "22": "a #1 #2 #3", "23": "a #1 #2 #3", "24": "a #1 #2 #3", "25": "a #1 #2 #3", "26": "a #1 #2 #3", "27": "a #1 #2 #3", "28": "a #1 #2 #3", "29": "a #1 #2", "30": "a #1 #2", "31": "a #1 #2", "32": "a #1 #2", "33": "a #1 #2", "34": "a #1 #2", "35": "a #1 #2", "36": "a #1 #2", "37": "a #1 #2", "38": "a #1 #2", "39": "a #1 #2", "40": "a #1 #2", "41": "a #1 #2", "42": "a #1 #2", "43": "a #1 #2", "44": "a #1 #2", "45": "a #1 #2", "46": "a #1 #2", "47": "a #1 #2", "48": "a #1 #2", "49": "a #2", "50": "a #2", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y"}, "chrome": {"4": "u", "5": "u", "6": "u", "7": "u", "8": "u", "9": "u", "10": "u", "11": "u", "12": "u", "13": "u", "14": "u", "15": "a #1 #2 #3", "16": "a #1 #2 #3", "17": "a #1 #2 #3", "18": "a #1 #2 #3", "19": "a #1 #2 #3", "20": "a #1 #2 #3", "21": "a #1 #2 #3", "22": "a #1 #2 #3", "23": "a #1 #2 #3", "24": "a #1 #2 #3", "25": "a #1 #2", "26": "a #1 #2", "27": "a #1 #2", "28": "a #1 #2", "29": "a #1 #2", "30": "a #1 #2", "31": "a #1 #2", "32": "a #1 #2", "33": "a #1 #2", "34": "a #1 #2", "35": "a #1 #2", "36": "a #1 #2", "37": "a #1 #2", "38": "a #1 #2", "39": "a #1 #2", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y", "63": "y", "64": "y", "65": "y", "66": "y", "67": "y", "68": "y", "69": "y"}, "safari": {"3.1": "u", "3.2": "u", "4": "u", "5": "u", "5.1": "a #1 #2 #3", "6": "a #1 #2 #3", "6.1": "a #1 #2 #3", "7": "a #1 #2 #3", "7.1": "a #1 #2", "8": "a #1 #2", "9": "a #1 #2", "9.1": "a #1 #2", "10": "y", "10.1": "y", "11": "y", "11.1": "y", "TP": "y"}, "opera": {"9": "u", "9.5-9.6": "u", "10.0-10.1": "u", "10.5": "u", "10.6": "u", "11": "u", "11.1": "u", "11.5": "u", "11.6": "a #1 #2 #3", "12": "a #1 #2 #3", "12.1": "a #1 #2 #3", "15": "a #1 #2", "16": "a #1 #2", "17": "a #1 #2", "18": "a #1 #2", "19": "a #1 #2", "20": "a #1 #2", "21": "a #1 #2", "22": "a #1 #2", "23": "a #1 #2", "24": "a #1 #2", "25": "a #1 #2", "26": "a #1 #2", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y"}, "ios_saf": {"3.2": "u", "4.0-4.1": "u", "4.2-4.3": "u", "5.0-5.1": "a #1 #2 #3", "6.0-6.1": "a #1 #2 #3", "7.0-7.1": "a #1 #2", "8": "a #1 #2", "8.1-8.4": "a #1 #2", "9.0-9.2": "a #1 #2", "9.3": "a #1 #2", "10.0-10.2": "y", "10.3": "y", "11.0-11.2": "y", "11.3": "y"}, "op_mini": {"all": "n"}, "android": {"2.1": "u", "2.2": "u", "2.3": "u", "3": "u", "4": "a #1 #2 #3", "4.1": "a #1 #2 #3", "4.2-4.3": "a #1 #2 #3", "4.4": "a #1 #2", "4.4.3-4.4.4": "a #1 #2", "62": "y"}, "bb": {"7": "u", "10": "a #1 #2"}, "op_mob": {"10": "u", "11": "u", "11.1": "u", "11.5": "u", "12": "a #1 #2 #3", "12.1": "a #1 #2 #3", "37": "y"}, "and_chr": {"66": "y"}, "and_ff": {"57": "y"}, "ie_mob": {"10": "a #1 #2 #3", "11": "a #1 #2 #3"}, "and_uc": {"11.8": "y"}, "samsung": {"4": "y", "5": "y", "6.2": "y"}, "and_qq": {"1.2": "a #1 #2"}, "baidu": {"7.12": "y"}}, "notes": "", "notes_by_num": {"1": "Does not support `reportValidity`", "2": "Does not support `validity.tooShort`. See also [support for `minlength`.](https://caniuse.com/#feat=input-minlength)", "3": "Does not support `validity.badInput`"}, "usage_perc_y": 86.25, "usage_perc_a": 8.74, "ucprefix": false, "parent": "", "keywords": "", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}