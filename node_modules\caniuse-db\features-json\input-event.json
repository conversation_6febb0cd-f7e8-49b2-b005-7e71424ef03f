{"title": "input event", "description": "The `input` event is fired when the user changes the value of an `<input>` element, `<select>` element, or `<textarea>` element. By contrast, the \"change\" event usually only fires after the form control has lost focus.", "spec": "https://html.spec.whatwg.org/multipage/forms.html#event-input-input", "status": "ls", "links": [{"url": "https://html.spec.whatwg.org/multipage/forms.html#send-select-update-notifications", "title": "Specification for `<select>` elements firing the `input` event"}, {"url": "https://developer.mozilla.org/en-US/docs/Web/Events/input", "title": "MDN Web Docs - input event"}, {"url": "https://wpdev.uservoice.com/forums/257854-microsoft-edge-developer/suggestions/10182111--input-type-checkbox-type-radio-should-fire-in", "title": "Microsoft Edge feature request on UserVoice"}], "bugs": [{"description": "Internet Explorer 10 and 11 fire the `input` event when an input field with a placeholder [is focused](https://connect.microsoft.com/IE/feedback/details/885747/ie-11-fires-the-input-event-when-a-input-field-with-placeholder-is-focused) or on page load when the placeholder [contains certain characters](https://jsfiddle.net/yiminghe/fm3rckaz/), like Chinese. "}], "categories": ["DOM", "HTML5"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "a #1 #3 #5", "10": "y #3 #5", "11": "y #3 #5"}, "edge": {"12": "y #3 #5", "13": "y #3 #5", "14": "y #3 #5", "15": "y #3 #5", "16": "y #3 #5", "17": "y #3 #5", "18": "y #3 #5"}, "firefox": {"2": "u", "3": "a #2 #3 #4", "3.5": "a #2 #3 #4", "3.6": "y #3 #4", "4": "y #3 #4", "5": "y #3 #4", "6": "y #3 #4", "7": "y #3 #4", "8": "y #3 #4", "9": "y #3 #4", "10": "y #3 #4", "11": "y #3 #4", "12": "y #3 #4", "13": "y #3 #4", "14": "y #3 #4", "15": "y #3 #4", "16": "y #3 #4", "17": "y #3 #4", "18": "y #3 #4", "19": "y #3 #4", "20": "y #3 #4", "21": "y #3 #4", "22": "y #3 #4", "23": "y #3 #4", "24": "y #3 #4", "25": "y #3 #4", "26": "y #3 #4", "27": "y #3 #4", "28": "y #3 #4", "29": "y #3 #4", "30": "y #3 #4", "31": "y #3 #4", "32": "y #3 #4", "33": "y #3 #4", "34": "y #3 #4", "35": "y #3 #4", "36": "y #3 #4", "37": "y #3 #4", "38": "y #3 #4", "39": "y #3 #4", "40": "y #3 #4", "41": "y #3 #4", "42": "y #3 #4", "43": "y #3 #4", "44": "y #3 #4", "45": "y #3 #4", "46": "y #3 #4", "47": "y #3 #4", "48": "y #3 #4", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y"}, "chrome": {"4": "u", "5": "u", "6": "u", "7": "u", "8": "u", "9": "u", "10": "u", "11": "u", "12": "u", "13": "u", "14": "u", "15": "y #3 #4", "16": "y #3 #4", "17": "y #3 #4", "18": "y #3 #4", "19": "y #3 #4", "20": "y #3 #4", "21": "y #3 #4", "22": "y #3 #4", "23": "y #3 #4", "24": "y #3 #4", "25": "y #3 #4", "26": "y #3 #4", "27": "y #3 #4", "28": "y #3 #4", "29": "y #3 #4", "30": "y #3 #4", "31": "y #3 #4", "32": "y #3 #4", "33": "y #3 #4", "34": "y #3 #4", "35": "y #4", "36": "y #4", "37": "y #4", "38": "y #4", "39": "y #4", "40": "y #4", "41": "y #4", "42": "y #4", "43": "y #4", "44": "y #4", "45": "y #4", "46": "y #4", "47": "y #4", "48": "y #4", "49": "y #4", "50": "y #4", "51": "y #4", "52": "y #4", "53": "y #4", "54": "y #4", "55": "y #4", "56": "y #4", "57": "y #4", "58": "y #4", "59": "y #4", "60": "y #4", "61": "y #4", "62": "y #4", "63": "y #4", "64": "y #4", "65": "y #4", "66": "y", "67": "y", "68": "y", "69": "y"}, "safari": {"3.1": "u", "3.2": "u", "4": "u", "5": "u", "5.1": "y #3 #4", "6": "u", "6.1": "y #4", "7": "y #4", "7.1": "y #4", "8": "y #4", "9": "y #4", "9.1": "y #4", "10": "y #4", "10.1": "y #4", "11": "y #4", "11.1": "y #4", "TP": "y #4"}, "opera": {"9": "u", "9.5-9.6": "u", "10.0-10.1": "u", "10.5": "u", "10.6": "u", "11": "u", "11.1": "u", "11.5": "u", "11.6": "a #2", "12": "u", "12.1": "y", "15": "y #3 #4", "16": "y #3 #4", "17": "y #3 #4", "18": "y #3 #4", "19": "y #3 #4", "20": "y #3 #4", "21": "y #3 #4", "22": "y #4", "23": "y #4", "24": "y #4", "25": "y #4", "26": "y #4", "27": "y #4", "28": "y #4", "29": "y #4", "30": "y #4", "31": "y #4", "32": "y #4", "33": "y #4", "34": "y #4", "35": "y #4", "36": "y #4", "37": "y #4", "38": "y #4", "39": "y #4", "40": "y #4", "41": "y #4", "42": "y #4", "43": "y #4", "44": "y #4", "45": "y #4", "46": "y #4", "47": "y #4", "48": "y #4", "49": "y #4", "50": "y #4", "51": "y #4", "52": "y"}, "ios_saf": {"3.2": "u", "4.0-4.1": "u", "4.2-4.3": "u", "5.0-5.1": "y #3 #4", "6.0-6.1": "y #3 #4", "7.0-7.1": "y #3 #4", "8": "y #4", "8.1-8.4": "y #4", "9.0-9.2": "y #4", "9.3": "y #4", "10.0-10.2": "y #4", "10.3": "y #4", "11.0-11.2": "y #4", "11.3": "y #4"}, "op_mini": {"all": "n"}, "android": {"2.1": "u", "2.2": "u", "2.3": "y #3 #4", "3": "y #3 #4", "4": "y #3 #4", "4.1": "y #3 #4", "4.2-4.3": "y #3 #4", "4.4": "y #3 #4", "4.4.3-4.4.4": "y #4", "62": "y #4"}, "bb": {"7": "y #3 #4", "10": "y #4"}, "op_mob": {"10": "y", "11": "y", "11.1": "y", "11.5": "y", "12": "y", "12.1": "y", "37": "y #4"}, "and_chr": {"66": "y #4"}, "and_ff": {"57": "y #3 #4"}, "ie_mob": {"10": "y #3 #5", "11": "y #3 #5"}, "and_uc": {"11.8": "y #3 #4"}, "samsung": {"4": "y #4", "5": "y #4", "6.2": "y #4"}, "and_qq": {"1.2": "y #4"}, "baidu": {"7.12": "y #4"}}, "notes": "", "notes_by_num": {"1": "Doesn't fire an `input` event when deleting text (via Backspace, Delete, Cut, etc.).", "2": "Doesn't fire an `input` event when drag-and-dropping text into an `<input>` or `<textarea>`.", "3": "`<select>` doesn't fire `input` events. See [MS Edge bug](https://developer.microsoft.com/microsoft-edge/platform/issues/4660045/) and [Firefox bug](https://bugzilla.mozilla.org/show_bug.cgi?id=1024350).", "4": "Doesn't fire an `input` event when (un)checking a checkbox or radio button, or when changing the selected file(s) of an `<input type=\"file\">`. See [Chrome bug](https://code.google.com/p/chromium/issues/detail?id=534245), [WebKit bug](https://bugs.webkit.org/show_bug.cgi?id=149398), and [Firefox bug](https://bugzilla.mozilla.org/show_bug.cgi?id=1206616).", "5": "Doesn't fire an `input` event when (un)checking a checkbox or radio button. See [MS Edge bug](https://connect.microsoft.com/IE/feedback/details/1883692)."}, "usage_perc_y": 94.99, "usage_perc_a": 0.14, "ucprefix": false, "parent": "", "keywords": "oninput", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}