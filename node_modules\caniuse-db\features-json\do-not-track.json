{"title": "Do Not Track API", "description": "Allows the browser's Do Not Track setting to be queried via `navigator.doNotTrack`.", "spec": "https://www.w3.org/TR/tracking-dnt/", "status": "cr", "links": [{"url": "https://developer.mozilla.org/en-US/docs/Web/API/Navigator/doNotTrack", "title": "MDN Web Docs - doNotTrack"}], "bugs": [{"description": "For Gecko versions < 32, some browsers incorrectly report 'yes' for both \"Tell sites I don't want to be tracked\" _and_ \"Tell sites I _do_ want to be tracked.\"."}], "categories": ["JS API"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "a x #1", "10": "a x #1", "11": "a #2"}, "edge": {"12": "a #2", "13": "a #2", "14": "a #2", "15": "a #2", "16": "a #2", "17": "y", "18": "y"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "a #3", "10": "a #3", "11": "a #3", "12": "a #3", "13": "a #3", "14": "a #3", "15": "a #3", "16": "a #3", "17": "a #3", "18": "a #3", "19": "a #3", "20": "a #3", "21": "a #3", "22": "a #3", "23": "a #3", "24": "a #3", "25": "a #3", "26": "a #3", "27": "a #3", "28": "a #3", "29": "a #3", "30": "a #3", "31": "a #3", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y", "63": "y", "64": "y", "65": "y", "66": "y", "67": "y", "68": "y", "69": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "y", "6": "y", "6.1": "a #4", "7": "a #4", "7.1": "a #4", "8": "a #4", "9": "a #4", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "11.1": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "y", "12.1": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "a #4", "8": "a #4", "8.1-8.4": "a #4", "9.0-9.2": "y", "9.3": "y", "10.0-10.2": "y", "10.3": "y", "11.0-11.2": "y", "11.3": "y"}, "op_mini": {"all": "y"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "y", "4.4.3-4.4.4": "y", "62": "y"}, "bb": {"7": "u", "10": "a #4"}, "op_mob": {"10": "u", "11": "u", "11.1": "u", "11.5": "u", "12": "u", "12.1": "y", "37": "y"}, "and_chr": {"66": "y"}, "and_ff": {"57": "y"}, "ie_mob": {"10": "a x #1", "11": "a #2"}, "and_uc": {"11.8": "y"}, "samsung": {"4": "y", "5": "y", "6.2": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "Partial support refers to the doNotTrack field being misnamed, or being attached to an object other than `navigator` (e.g. `window`).", "notes_by_num": {"1": "IE 9 and 10 are vendor-prefixed as `navigator.msDoNotTrack`", "2": "IE 11 and Edge use `window.doNotTrack` instead of `navigator.doNotTrack`.", "3": "Browsers based on Gecko versions prior to 32 used 'yes' and 'no' rather than '1' and '0'.", "4": "Between versions 6.1.1 and 9.1.3, <PERSON>fari supported an ultimately-rejected amendment to the spec, using `window.doNotTrack` rather than `navigator.doNotTrack`."}, "usage_perc_y": 91.86, "usage_perc_a": 5.48, "ucprefix": false, "parent": "", "keywords": "DNT", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}