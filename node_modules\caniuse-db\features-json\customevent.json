{"title": "CustomEvent", "description": "A DOM event interface that can carry custom application-defined data.", "spec": "https://dom.spec.whatwg.org/#interface-customevent", "status": "ls", "links": [{"url": "https://developer.mozilla.org/en-US/docs/Web/API/CustomEvent", "title": "MDN Web Docs - CustomEvent"}, {"url": "https://github.com/krambuhl/custom-event-polyfill", "title": "Polyfill based on the MDN snippet"}, {"url": "https://github.com/jonathantneal/EventListener", "title": "EventListener polyfill which includes a CustomEvent polyfill"}], "bugs": [], "categories": ["DOM", "JS API"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "a #1", "10": "a #1", "11": "a #1"}, "edge": {"12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "a #1", "7": "a #1", "8": "a #1", "9": "a #1", "10": "a #1", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y"}, "chrome": {"4": "n", "5": "u", "6": "u", "7": "u", "8": "u", "9": "a #1 #2", "10": "a #1 #2", "11": "a #1 #2", "12": "a #1 #2", "13": "u", "14": "u", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y", "63": "y", "64": "y", "65": "y", "66": "y", "67": "y", "68": "y", "69": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "u", "5.1": "a #1 #2", "6": "u", "6.1": "y", "7": "y", "7.1": "y", "8": "y", "9": "y", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "11.1": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "a #1", "11.1": "a #1", "11.5": "a #1", "11.6": "y", "12": "y", "12.1": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y"}, "ios_saf": {"3.2": "u", "4.0-4.1": "n", "4.2-4.3": "u", "5.0-5.1": "a #1 #2", "6.0-6.1": "y", "7.0-7.1": "y", "8": "y", "8.1-8.4": "y", "9.0-9.2": "y", "9.3": "y", "10.0-10.2": "y", "10.3": "y", "11.0-11.2": "y", "11.3": "y"}, "op_mini": {"all": "y"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "a #1 #2", "4": "a #1 #2", "4.1": "a #1 #2", "4.2-4.3": "a #1 #2", "4.4": "y", "4.4.3-4.4.4": "y", "62": "y"}, "bb": {"7": "a #1 #2", "10": "y"}, "op_mob": {"10": "n", "11": "a #1", "11.1": "a #1", "11.5": "a #1", "12": "y", "12.1": "y", "37": "y"}, "and_chr": {"66": "y"}, "and_ff": {"57": "y"}, "ie_mob": {"10": "a #1", "11": "a #1"}, "and_uc": {"11.8": "y"}, "samsung": {"4": "y", "5": "y", "6.2": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "Not supported in some versions of Android's old WebKit-based WebView.", "notes_by_num": {"1": "While a `window.CustomEvent` object exists, it cannot be called as a constructor. Instead of `new CustomEvent(...)`, you must use `e = document.createEvent('CustomEvent')` and then `e.initCustomEvent(...)`", "2": "There is no `window.CustomEvent` object, but `document.createEvent('CustomEvent')` still works."}, "usage_perc_y": 94.13, "usage_perc_a": 3.68, "ucprefix": false, "parent": "", "keywords": "custom events,custom,event", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}