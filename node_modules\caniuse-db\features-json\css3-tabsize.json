{"title": "CSS3 tab-size", "description": "Method of customizing the width of the tab character. Only effective using 'white-space: pre' or 'white-space: pre-wrap'.", "spec": "https://www.w3.org/TR/css3-text/#tab-size", "status": "wd", "links": [{"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/tab-size", "title": "MDN Web Docs - CSS tab-size"}, {"url": "https://wpdev.uservoice.com/forums/257854-microsoft-edge-developer/suggestions/6524689-tab-size-property", "title": "Microsoft Edge feature request on UserVoice"}], "bugs": [{"description": "Firefox up to and including 52 [did not](https://bugzilla.mozilla.org/show_bug.cgi?id=943918) support `<length>` values"}], "categories": ["CSS3"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "a x #1", "5": "a x #1", "6": "a x #1", "7": "a x #1", "8": "a x #1", "9": "a x #1", "10": "a x #1", "11": "a x #1", "12": "a x #1", "13": "a x #1", "14": "a x #1", "15": "a x #1", "16": "a x #1", "17": "a x #1", "18": "a x #1", "19": "a x #1", "20": "a x #1", "21": "a x #1", "22": "a x #1", "23": "a x #1", "24": "a x #1", "25": "a x #1", "26": "a x #1", "27": "a x #1", "28": "a x #1", "29": "a x #1", "30": "a x #1", "31": "a x #1", "32": "a x #1", "33": "a x #1", "34": "a x #1", "35": "a x #1", "36": "a x #1", "37": "a x #1", "38": "a x #1", "39": "a x #1", "40": "a x #1", "41": "a x #1", "42": "a x #1", "43": "a x #1", "44": "a x #1", "45": "a x #1", "46": "a x #1", "47": "a x #1", "48": "a x #1", "49": "a x #1", "50": "a x #1", "51": "a x #1", "52": "a x #1", "53": "y x", "54": "y x", "55": "y x", "56": "y x", "57": "y x", "58": "y x", "59": "y x", "60": "y x", "61": "y x"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "a #1", "22": "a #1", "23": "a #1", "24": "a #1", "25": "a #1", "26": "a #1", "27": "a #1", "28": "a #1", "29": "a #1", "30": "a #1", "31": "a #1", "32": "a #1", "33": "a #1", "34": "a #1", "35": "a #1", "36": "a #1", "37": "a #1", "38": "a #1", "39": "a #1", "40": "a #1", "41": "a #1", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y", "63": "y", "64": "y", "65": "y", "66": "y", "67": "y", "68": "y", "69": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "a #1", "7": "a #1", "7.1": "a #1", "8": "a #1", "9": "a #1", "9.1": "a #1", "10": "a #1", "10.1": "a #1", "11": "a #1", "11.1": "a #1", "TP": "a #1"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "a x #1", "11": "a x #1", "11.1": "a x #1", "11.5": "a x #1", "11.6": "a x #1", "12": "a x #1", "12.1": "a x #1", "15": "a #1", "16": "a #1", "17": "a #1", "18": "a #1", "19": "a #1", "20": "a #1", "21": "a #1", "22": "a #1", "23": "a #1", "24": "a #1", "25": "a #1", "26": "a #1", "27": "a #1", "28": "a #1", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "a #1", "8": "a #1", "8.1-8.4": "a #1", "9.0-9.2": "a #1", "9.3": "a #1", "10.0-10.2": "a #1", "10.3": "a #1", "11.0-11.2": "a #1", "11.3": "a #1"}, "op_mini": {"all": "a x #1"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "a #1", "4.4.3-4.4.4": "a #1", "62": "y"}, "bb": {"7": "a #1", "10": "a #1"}, "op_mob": {"10": "n", "11": "a x #1", "11.1": "a x #1", "11.5": "a x #1", "12": "a x #1", "12.1": "a x #1", "37": "y"}, "and_chr": {"66": "y"}, "and_ff": {"57": "y x"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.8": "y"}, "samsung": {"4": "y", "5": "y", "6.2": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "", "notes_by_num": {"1": "Partial refers to supporting `<integer>` but not `<length>` values."}, "usage_perc_y": 73.62, "usage_perc_a": 18.67, "ucprefix": false, "parent": "", "keywords": "tab-size,tab-width", "ie_id": "csstabsizeproperty", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}