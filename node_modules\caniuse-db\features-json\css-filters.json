{"title": "CSS Filter Effects", "description": "Method of applying filter effects (like blur, grayscale, brightness, contrast and hue) to elements, previously only possible by using SVG.", "spec": "https://www.w3.org/TR/filter-effects-1/", "status": "wd", "links": [{"url": "http://html5-demos.appspot.com/static/css/filters/index.html", "title": "Demo file for WebKit browsers"}, {"url": "https://www.html5rocks.com/en/tutorials/filters/understanding-css/", "title": "HTML5Rocks article"}, {"url": "http://dl.dropbox.com/u/3260327/angular/CSS3ImageManipulation.html", "title": "Filter editor"}, {"url": "http://bennettfeely.com/filters/", "title": "Filter Playground"}], "bugs": [{"description": "In Edge filter won't apply if the element or it's parent has negative z-index. [See bug](https://developer.microsoft.com/en-us/microsoft-edge/platform/issues/9318580/)."}], "categories": ["CSS", "CSS3"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n d #2 #4", "13": "a #4", "14": "a #4", "15": "a #4", "16": "a #4", "17": "a #4", "18": "a #4"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "a #3", "4": "a #3", "5": "a #3", "6": "a #3", "7": "a #3", "8": "a #3", "9": "a #3", "10": "a #3", "11": "a #3", "12": "a #3", "13": "a #3", "14": "a #3", "15": "a #3", "16": "a #3", "17": "a #3", "18": "a #3", "19": "a #3", "20": "a #3", "21": "a #3", "22": "a #3", "23": "a #3", "24": "a #3", "25": "a #3", "26": "a #3", "27": "a #3", "28": "a #3", "29": "a #3", "30": "a #3", "31": "a #3", "32": "a #3", "33": "a #3", "34": "a d #1", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "y x", "19": "y x", "20": "y x", "21": "y x", "22": "y x", "23": "y x", "24": "y x", "25": "y x", "26": "y x", "27": "y x", "28": "y x", "29": "y x", "30": "y x", "31": "y x", "32": "y x", "33": "y x", "34": "y x", "35": "y x", "36": "y x", "37": "y x", "38": "y x", "39": "y x", "40": "y x", "41": "y x", "42": "y x", "43": "y x", "44": "y x", "45": "y x", "46": "y x", "47": "y x", "48": "y x", "49": "y x", "50": "y x", "51": "y x", "52": "y x", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y", "63": "y", "64": "y", "65": "y", "66": "y", "67": "y", "68": "y", "69": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "y x", "6.1": "y x", "7": "y x", "7.1": "y x", "8": "y x", "9": "y x", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "11.1": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "y x", "16": "y x", "17": "y x", "18": "y x", "19": "y x", "20": "y x", "21": "y x", "22": "y x", "23": "y x", "24": "y x", "25": "y x", "26": "y x", "27": "y x", "28": "y x", "29": "y x", "30": "y x", "31": "y x", "32": "y x", "33": "y x", "34": "y x", "35": "y x", "36": "y x", "37": "y x", "38": "y x", "39": "y x", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "y x", "7.0-7.1": "y x", "8": "y x", "8.1-8.4": "y x", "9.0-9.2": "y x", "9.3": "y", "10.0-10.2": "y", "10.3": "y", "11.0-11.2": "y", "11.3": "y"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "y x", "4.4.3-4.4.4": "y x", "62": "y"}, "bb": {"7": "n", "10": "y x"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "y x"}, "and_chr": {"66": "y"}, "and_ff": {"57": "y"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.8": "y"}, "samsung": {"4": "y x", "5": "y x", "6.2": "y x"}, "and_qq": {"1.2": "y x"}, "baidu": {"7.12": "y x"}}, "notes": "Note that this property is significantly different from and incompatible with Microsoft's [older \"filter\" property](http://msdn.microsoft.com/en-us/library/ie/ms530752%28v=vs.85%29.aspx).", "notes_by_num": {"1": "Supported in Firefox under the `layout.css.filters.enabled` flag.", "2": "Supported in MS Edge under the \"Enable CSS filter property\" flag.", "3": "Partial support in Firefox before version 34 [only implemented the url() function of the filter property](https://developer.mozilla.org/en-US/docs/Web/CSS/filter#Browser_compatibility)", "4": "Partial support refers to supporting filter functions, but not the `url` function."}, "usage_perc_y": 89.38, "usage_perc_a": 2.06, "ucprefix": false, "parent": "", "keywords": "sepia,hue-rotate,invert,saturate,filter:blur", "ie_id": "filters", "chrome_id": "5822463824887808", "firefox_id": "", "webkit_id": "", "shown": true}