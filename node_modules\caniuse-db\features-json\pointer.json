{"title": "Pointer events", "description": "This specification integrates various inputs from mice, touchscreens, and pens, making separate implementations no longer necessary and authoring for cross-device pointers easier. Not to be mistaken with the unrelated \"pointer-events\" CSS property.", "spec": "https://www.w3.org/TR/pointerevents/", "status": "rec", "links": [{"url": "http://blogs.msdn.com/b/ie/archive/2011/09/20/touch-input-for-ie10-and-metro-style-apps.aspx", "title": "Implementation of Pointer Events in IE10"}, {"url": "http://blogs.msdn.com/b/eternalcoding/archive/2013/01/16/hand-js-a-polyfill-for-supporting-pointer-events-on-every-browser.aspx", "title": "Hand.js, the polyfill for browsers only supporting Touch Events"}, {"url": "http://blogs.msdn.com/b/davrous/archive/2013/02/20/handling-touch-in-your-html5-apps-thanks-to-the-pointer-events-of-ie10-and-windows-8.aspx", "title": "Article & tutorial"}, {"url": "http://deeptissuejs.com", "title": "Abstraction library for pointer events"}, {"url": "https://github.com/jquery/PEP", "title": "PEP: Pointer Events Polyfill"}, {"url": "https://developer.mozilla.org/en-US/docs/Web/API/Pointer_events", "title": "Pointer Event API on MDN"}, {"url": "https://bugzilla.mozilla.org/show_bug.cgi?id=822898", "title": "Bugzilla@Mozilla: Bug 822898 - Implement pointer events"}], "bugs": [], "categories": ["DOM", "JS API"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "a x #1", "11": "y"}, "edge": {"12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "p", "7": "p", "8": "p", "9": "p", "10": "p", "11": "p", "12": "p", "13": "p", "14": "p", "15": "p", "16": "p", "17": "p", "18": "p", "19": "p", "20": "p", "21": "p", "22": "p", "23": "p", "24": "p", "25": "p", "26": "p", "27": "p", "28": "p", "29": "p", "30": "p", "31": "p", "32": "p", "33": "p", "34": "p", "35": "p", "36": "p", "37": "p", "38": "p", "39": "p", "40": "p", "41": "p d #2", "42": "p d #2", "43": "p d #2", "44": "p d #2", "45": "p d #2", "46": "p d #2", "47": "p d #2", "48": "p d #2", "49": "p d #2", "50": "p d #2", "51": "p d #2", "52": "p d #2", "53": "p d #2", "54": "p d #2", "55": "p d #2", "56": "p d #2", "57": "p d #2", "58": "p d #2", "59": "y", "60": "y", "61": "y"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "p", "23": "p", "24": "p", "25": "p", "26": "p", "27": "p", "28": "p", "29": "p", "30": "p", "31": "p", "32": "p", "33": "p", "34": "p", "35": "p", "36": "p", "37": "p", "38": "p", "39": "p", "40": "p", "41": "p", "42": "p", "43": "p", "44": "p", "45": "p", "46": "p", "47": "p", "48": "p", "49": "p", "50": "p", "51": "p", "52": "p d #3", "53": "p d #3", "54": "p d #3", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y", "63": "y", "64": "y", "65": "y", "66": "y", "67": "y", "68": "y", "69": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "p", "7": "p", "7.1": "p", "8": "p", "9": "p", "9.1": "p", "10": "p", "10.1": "p", "11": "p", "11.1": "p", "TP": "p"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "p", "16": "p", "17": "p", "18": "p", "19": "p", "20": "p", "21": "p", "22": "p", "23": "p", "24": "p", "25": "p", "26": "p", "27": "p", "28": "p", "29": "p", "30": "p", "31": "p", "32": "p", "33": "p", "34": "p", "35": "p", "36": "p", "37": "p", "38": "p", "39": "p d #3", "40": "p d #3", "41": "p d #3", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y"}, "ios_saf": {"3.2": "p", "4.0-4.1": "p", "4.2-4.3": "p", "5.0-5.1": "p", "6.0-6.1": "p", "7.0-7.1": "p", "8": "p", "8.1-8.4": "p", "9.0-9.2": "p", "9.3": "p", "10.0-10.2": "p", "10.3": "p", "11.0-11.2": "p", "11.3": "p"}, "op_mini": {"all": "n"}, "android": {"2.1": "p", "2.2": "p", "2.3": "p", "3": "p", "4": "p", "4.1": "p", "4.2-4.3": "p", "4.4": "p", "4.4.3-4.4.4": "p", "62": "y"}, "bb": {"7": "p", "10": "p"}, "op_mob": {"10": "n", "11": "p", "11.1": "p", "11.5": "p", "12": "p", "12.1": "p", "37": "p"}, "and_chr": {"66": "y"}, "and_ff": {"57": "p d #2"}, "ie_mob": {"10": "a x", "11": "y"}, "and_uc": {"11.8": "p"}, "samsung": {"4": "p", "5": "n", "6.2": "y"}, "and_qq": {"1.2": "p d #3"}, "baidu": {"7.12": "n"}}, "notes": "Firefox, starting with version 28, provides the 'dom.w3c_pointer_events.enabled' flag to support this specification.", "notes_by_num": {"1": "Partial support in IE10 refers the lack of pointerenter and pointerleave events.", "2": "Firefox support is disabled by default and [only supports mouse input](https://hacks.mozilla.org/2015/08/pointer-events-now-in-firefox-nightly/). On Windows only, touch can be enabled with the `layers.async-pan-zoom.enabled` and `dom.w3c_touch_events.enabled` flags", "3": "Can be enabled with the `#enable-pointer-events` flag."}, "usage_perc_y": 65.7, "usage_perc_a": 0.15, "ucprefix": false, "parent": "", "keywords": "pointerdown,pointermove,pointerup,pointercancel,pointerover,pointerout,pointerenter,pointerleave", "ie_id": "pointerevents", "chrome_id": "4504699138998272", "firefox_id": "pointer-events", "webkit_id": "", "shown": true}