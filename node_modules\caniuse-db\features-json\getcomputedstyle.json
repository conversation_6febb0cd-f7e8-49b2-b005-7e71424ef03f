{"title": "getComputedStyle", "description": "API to get the current computed CSS styles applied to an element. This may be the current value applied by an animation or as set by a stylesheet.", "spec": "https://www.w3.org/TR/cssom/#dom-window-getcomputedstyle", "status": "rec", "links": [{"url": "https://developer.mozilla.org/en/DOM/window.getComputedStyle", "title": "MDN Web Docs - getComputedStyle"}, {"url": "https://testdrive-archive.azurewebsites.net/HTML5/getComputedStyle/", "title": "Demo"}, {"url": "http://snipplr.com/view/13523/", "title": "Polyfill for IE"}, {"url": "https://www.webplatform.org/docs/css/cssom/methods/getComputedStyle", "title": "WebPlatform Docs"}], "bugs": [], "categories": ["CSS3", "DOM", "JS API"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "y", "10": "y", "11": "y"}, "edge": {"12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y"}, "firefox": {"2": "n", "3": "a #1", "3.5": "a #1", "3.6": "a #1", "4": "y", "5": "y", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y"}, "chrome": {"4": "a #2", "5": "a #2", "6": "a #2", "7": "a #2", "8": "a #2", "9": "a #2", "10": "a #2", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y", "63": "y", "64": "y", "65": "y", "66": "y", "67": "y", "68": "y", "69": "y"}, "safari": {"3.1": "a #2", "3.2": "a #2", "4": "a #2", "5": "y", "5.1": "y", "6": "y", "6.1": "y", "7": "y", "7.1": "y", "8": "y", "9": "y", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "11.1": "y", "TP": "y"}, "opera": {"9": "a #2", "9.5-9.6": "a #2", "10.0-10.1": "a #2", "10.5": "a #2", "10.6": "y", "11": "y", "11.1": "y", "11.5": "y", "11.6": "y", "12": "y", "12.1": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y"}, "ios_saf": {"3.2": "a #2", "4.0-4.1": "a #2", "4.2-4.3": "a #2", "5.0-5.1": "y", "6.0-6.1": "y", "7.0-7.1": "y", "8": "y", "8.1-8.4": "y", "9.0-9.2": "y", "9.3": "y", "10.0-10.2": "y", "10.3": "y", "11.0-11.2": "y", "11.3": "y"}, "op_mini": {"all": "a #2"}, "android": {"2.1": "a #2", "2.2": "a #2", "2.3": "a #2", "3": "a #2", "4": "y", "4.1": "y", "4.2-4.3": "y", "4.4": "y", "4.4.3-4.4.4": "y", "62": "y"}, "bb": {"7": "a #2", "10": "y"}, "op_mob": {"10": "a #2", "11": "y", "11.1": "y", "11.5": "y", "12": "y", "12.1": "y", "37": "y"}, "and_chr": {"66": "y"}, "and_ff": {"57": "y"}, "ie_mob": {"10": "y", "11": "y"}, "and_uc": {"11.8": "y"}, "samsung": {"4": "y", "5": "y", "6.2": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "", "notes_by_num": {"1": "Partial support refers to requiring the second parameter to be included.", "2": "Partial support refers to not supporting getComputedStyle on pseudo-elements."}, "usage_perc_y": 95.18, "usage_perc_a": 2.77, "ucprefix": false, "parent": "", "keywords": "", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}