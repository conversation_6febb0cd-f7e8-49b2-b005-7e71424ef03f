{"title": ":focus-visible CSS pseudo-class", "description": "The `:focus-visible` pseudo-class applies while an element matches the `:focus` pseudo-class, and the UA determines via heuristics that the focus should be specially indicated on the element (typically via a “focus ring”).", "spec": "https://drafts.csswg.org/selectors-4/#the-focus-visible-pseudo", "status": "unoff", "links": [{"url": "https://github.com/WICG/focus-visible", "title": "Prototype for `:focus-visible`"}, {"url": "https://bugs.webkit.org/show_bug.cgi?id=30523", "title": "WebKit bug #140144: Add support for `-webkit-focusring` CSS pseudo class"}, {"url": "https://bugs.chromium.org/p/chromium/issues/detail?id=271023", "title": "Chromium issue #271023: Outline should not appear on elements focused by mouse"}, {"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/:-moz-focusring", "title": "Mozilla Developer Network (MDN) documentation - :-moz-focusring"}, {"url": "https://wpdev.uservoice.com/forums/257854-microsoft-edge-developer/suggestions/19594159--focus-visible", "title": "Microsoft Edge implementation suggestion"}, {"url": "https://bugs.chromium.org/p/chromium/issues/detail?id=817199", "title": "Chrome does not support CSS Selectors 4 :focus-visible"}, {"url": "https://groups.google.com/a/chromium.org/forum/#!topic/blink-dev/-wN72ESFsyo", "title": "Blink: Intent to implement :focus-visible pseudo class."}, {"url": "https://bugzilla.mozilla.org/show_bug.cgi?id=1437901&GoAheadAndLogIn=1", "title": "Bugzilla: Add :focus-visible (former :focus-ring)"}], "bugs": [], "categories": ["CSS"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "y x #1", "5": "y x #1", "6": "y x #1", "7": "y x #1", "8": "y x #1", "9": "y x #1", "10": "y x #1", "11": "y x #1", "12": "y x #1", "13": "y x #1", "14": "y x #1", "15": "y x #1", "16": "y x #1", "17": "y x #1", "18": "y x #1", "19": "y x #1", "20": "y x #1", "21": "y x #1", "22": "y x #1", "23": "y x #1", "24": "y x #1", "25": "y x #1", "26": "y x #1", "27": "y x #1", "28": "y x #1", "29": "y x #1", "30": "y x #1", "31": "y x #1", "32": "y x #1", "33": "y x #1", "34": "y x #1", "35": "y x #1", "36": "y x #1", "37": "y x #1", "38": "y x #1", "39": "y x #1", "40": "y x #1", "41": "y x #1", "42": "y x #1", "43": "y x #1", "44": "y x #1", "45": "y x #1", "46": "y x #1", "47": "y x #1", "48": "y x #1", "49": "y x #1", "50": "y x #1", "51": "y x #1", "52": "y x #1", "53": "y x #1", "54": "y x #1", "55": "y x #1", "56": "y x #1", "57": "y x #1", "58": "y x #1", "59": "y x #1", "60": "y x #1", "61": "y x #1"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n", "46": "n", "47": "n", "48": "n", "49": "n", "50": "n", "51": "n", "52": "n", "53": "n", "54": "n", "55": "n", "56": "n", "57": "n", "58": "n", "59": "n", "60": "n", "61": "n", "62": "n", "63": "n", "64": "n", "65": "n", "66": "n", "67": "p d #2", "68": "p d #2", "69": "p d #2"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "n", "8": "n", "9": "n", "9.1": "n", "10": "n", "10.1": "n", "11": "n", "11.1": "n", "TP": "n"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n", "46": "n", "47": "n", "48": "n", "49": "n", "50": "n", "51": "n", "52": "n"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "n", "8.1-8.4": "n", "9.0-9.2": "n", "9.3": "n", "10.0-10.2": "n", "10.3": "n", "11.0-11.2": "n", "11.3": "n"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "62": "n"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "n"}, "and_chr": {"66": "n"}, "and_ff": {"57": "y x #1"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.8": "n"}, "samsung": {"4": "n", "5": "n", "6.2": "n"}, "and_qq": {"1.2": "n"}, "baidu": {"7.12": "n"}}, "notes": "Previously drafted as `:focus-ring`", "notes_by_num": {"1": "As `:-moz-focusring`", "2": "Enabled through the \"Experimental Web Platform features\" flag in chrome://flags"}, "usage_perc_y": 5.4, "usage_perc_a": 0, "ucprefix": false, "parent": "", "keywords": "focus,ring,focusring,focus-ring,visible,focus-visible,pseudo", "ie_id": "", "chrome_id": "5823526732824576", "firefox_id": "", "webkit_id": "", "shown": true}