{"title": "DOMMatrix", "description": "The `DOMMatrix` interface represents 4x4 matrices, suitable for 2D and 3D operations. Supersedes the `WebKitCSSMatrix` and `SVGMatrix` interfaces.", "spec": "https://drafts.fxtf.org/geometry/#dommatrix", "status": "cr", "links": [{"url": "https://developer.apple.com/reference/webkitjs/webkitcssmatrix", "title": "WebKitCSSMatrix API Reference"}, {"url": "https://compat.spec.whatwg.org/#webkitcssmatrix-interface", "title": "WebKitCSSMatrix in Compatibility Standard"}, {"url": "https://developer.mozilla.org/en-US/docs/Web/API/DOMMatrix", "title": "MDN Web Docs - DOMMatrix"}, {"url": "https://bugs.chromium.org/p/chromium/issues/detail?id=581955", "title": "Chrome implementation bug"}], "bugs": [], "categories": ["DOM"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "a #1", "11": "a #1"}, "edge": {"12": "a #1", "13": "a #1", "14": "a #1", "15": "a #1", "16": "a #1", "17": "a #1", "18": "a #1"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "a #3 #5", "34": "a #3 #5", "35": "a #3 #5", "36": "a #3 #5", "37": "a #3 #5", "38": "a #3 #5", "39": "a #3 #5", "40": "a #3 #5", "41": "a #3 #5", "42": "a #3 #5", "43": "a #3 #5", "44": "a #3 #5", "45": "a #3 #5", "46": "a #3 #5", "47": "a #3 #5", "48": "a #3 #5", "49": "a #4 #5", "50": "a #4 #5", "51": "a #4 #5", "52": "a #4 #5", "53": "a #4 #5", "54": "a #4 #5", "55": "a #4 #5", "56": "a #4 #5", "57": "a #4 #5", "58": "a #4 #5", "59": "a #4 #5", "60": "a #4 #5", "61": "a #4 #5"}, "chrome": {"4": "u", "5": "u", "6": "u", "7": "u", "8": "a #1 #2", "9": "a #1", "10": "a #1", "11": "a #1", "12": "a #1", "13": "a #1", "14": "a #1", "15": "a #1", "16": "a #1", "17": "a #1", "18": "a #1", "19": "a #1", "20": "a #1", "21": "a #1", "22": "a #1", "23": "a #1", "24": "a #1", "25": "a #1", "26": "a #1", "27": "a #1", "28": "a #1", "29": "a #1", "30": "a #1", "31": "a #1", "32": "a #1", "33": "a #1", "34": "a #1", "35": "a #1", "36": "a #1", "37": "a #1", "38": "a #1", "39": "a #1", "40": "a #1", "41": "a #1", "42": "a #1", "43": "a #1", "44": "a #1", "45": "a #1", "46": "a #1", "47": "a #1", "48": "a #1", "49": "a #1", "50": "a #1", "51": "a #1", "52": "a #1", "53": "a #1", "54": "a #1", "55": "a #1", "56": "a #1", "57": "a #1", "58": "a #1", "59": "a #1", "60": "a #1", "61": "a #4", "62": "a #4", "63": "a #4", "64": "a #4", "65": "a #4", "66": "a #4", "67": "a #4", "68": "a #4", "69": "a #4"}, "safari": {"3.1": "u", "3.2": "u", "4": "u", "5": "a #1", "5.1": "a #1", "6": "a #1", "6.1": "a #1", "7": "a #1", "7.1": "a #1", "8": "a #1", "9": "a #1", "9.1": "a #1", "10": "a #1", "10.1": "a #1", "11": "a #4", "11.1": "a #4", "TP": "a #4"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "a #1", "16": "a #1", "17": "a #1", "18": "a #1", "19": "a #1", "20": "a #1", "21": "a #1", "22": "a #1", "23": "a #1", "24": "a #1", "25": "a #1", "26": "a #1", "27": "a #1", "28": "a #1", "29": "a #1", "30": "a #1", "31": "a #1", "32": "a #1", "33": "a #1", "34": "a #1", "35": "a #1", "36": "a #1", "37": "a #1", "38": "a #1", "39": "a #1", "40": "a #1", "41": "a #1", "42": "a #1", "43": "a #1", "44": "a #1", "45": "a #1", "46": "a #1", "47": "a #1", "48": "a #4", "49": "a #4", "50": "a #4", "51": "a #4", "52": "a #4"}, "ios_saf": {"3.2": "u", "4.0-4.1": "u", "4.2-4.3": "u", "5.0-5.1": "a #1", "6.0-6.1": "a #1", "7.0-7.1": "a #1", "8": "a #1", "8.1-8.4": "a #1", "9.0-9.2": "a #1", "9.3": "a #1", "10.0-10.2": "a #1", "10.3": "a #1", "11.0-11.2": "a #1", "11.3": "a #1"}, "op_mini": {"all": "n"}, "android": {"2.1": "a x #2", "2.2": "a x #2", "2.3": "a x #2", "3": "a x #2", "4": "a #1", "4.1": "a #1", "4.2-4.3": "a #1", "4.4": "a #1", "4.4.3-4.4.4": "a #1", "62": "y"}, "bb": {"7": "u", "10": "a #1"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "a #1"}, "and_chr": {"66": "y"}, "and_ff": {"57": "y"}, "ie_mob": {"10": "a #1", "11": "a #1"}, "and_uc": {"11.8": "a #1"}, "samsung": {"4": "a #1", "5": "a #1", "6.2": "a #1"}, "and_qq": {"1.2": "a #1"}, "baidu": {"7.12": "a #1"}}, "notes": "", "notes_by_num": {"1": "Only supports the `WebKitCSSMatrix` version of the interface, not `DOMMatrix`", "2": "`WebKitCSSMatrix#skewX`, `WebKitCSSMatrix#skewY` are not supported", "3": "Only supports the `DOMMatrix` version of the interface, not `WebKitCSSMatrix` (support required by spec for legacy reasons)", "4": "Only replaces `WebkitCSSMatrix` and not `SVGMatrix`", "5": "Does not support `fromMatrix()`, `fromFloat32Array()`, `fromFloat64Array()`, and `toJSON()` methods"}, "usage_perc_y": 30.66, "usage_perc_a": 64.14, "ucprefix": false, "parent": "", "keywords": "matrix,WebKitCSSMatrix,MSCSSMatrix,CSSMatrix,DOMMatrix,DOMMatrixReadOnly", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}