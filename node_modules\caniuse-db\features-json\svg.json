{"title": "SVG (basic support)", "description": "Method of displaying basic Vector Graphics features using the embed or object elements. Refers to the SVG 1.1 spec.", "spec": "https://www.w3.org/TR/SVG/", "status": "rec", "links": [{"url": "https://en.wikipedia.org/wiki/Scalable_Vector_Graphics", "title": "Wikipedia"}, {"url": "http://www.alistapart.com/articles/using-svg-for-flexible-scalable-and-fun-backgrounds-part-i", "title": "A List Apart article"}, {"url": "http://svg-wow.org/", "title": "SVG showcase site"}, {"url": "http://code.google.com/p/svgweb/", "title": "SVG Web: Flash-based polyfill"}, {"url": "https://github.com/SVG-Edit/svgedit", "title": "Web-based SVG editor"}, {"url": "https://raw.github.com/phiggins42/has.js/master/detect/graphics.js#svg", "title": "has.js test"}], "bugs": [{"description": "Chrome 26 doesn't support the attribute `preserveAspectRatio=none`."}, {"description": "SVG graphics look pixelated when zooming in or using scaled up images in Opera Mini & Opera Mobile 12.1-."}, {"description": "IE9-Edge12, Safari 5.1-6, and UCWeb 11 do not support [referencing external files](https://css-tricks.com/svg-use-external-source/) via `<use xlink:href>`. Polyfills are available: [server-side inlining + snippet](https://codepen.io/hexalys/pen/epErZj/) - [script](https://github.com/jonathantneal/svg4everybody)"}, {"description": "Chrome 48+ [no longer has support](https://www.chromestatus.com/feature/5708851034718208) for the `SVGPathSeg` interface. Polyfills are available: [original](https://github.com/progers/pathseg/blob/master/pathseg.js) - [SVG 2 draft-based](https://github.com/jarek-foksa/path-data-polyfill.js)"}], "categories": ["SVG"], "stats": {"ie": {"5.5": "n", "6": "p", "7": "p", "8": "p", "9": "y #2", "10": "y #2", "11": "y #2"}, "edge": {"12": "y #2", "13": "y #2", "14": "y #2", "15": "y #2", "16": "y #2", "17": "y #2", "18": "y #2"}, "firefox": {"2": "a", "3": "y", "3.5": "y", "3.6": "y", "4": "y", "5": "y", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y"}, "chrome": {"4": "y", "5": "y", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y", "63": "y", "64": "y", "65": "y", "66": "y", "67": "y", "68": "y", "69": "y"}, "safari": {"3.1": "a", "3.2": "y", "4": "y", "5": "y", "5.1": "y", "6": "y", "6.1": "y", "7": "y", "7.1": "y", "8": "y", "9": "y", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "11.1": "y", "TP": "y"}, "opera": {"9": "y", "9.5-9.6": "y", "10.0-10.1": "y", "10.5": "y", "10.6": "y", "11": "y", "11.1": "y", "11.5": "y", "11.6": "y", "12": "y", "12.1": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y"}, "ios_saf": {"3.2": "y", "4.0-4.1": "y", "4.2-4.3": "y", "5.0-5.1": "y", "6.0-6.1": "y", "7.0-7.1": "y", "8": "y", "8.1-8.4": "y", "9.0-9.2": "y", "9.3": "y", "10.0-10.2": "y", "10.3": "y", "11.0-11.2": "y", "11.3": "y"}, "op_mini": {"all": "y"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "a #1", "4": "a #1", "4.1": "a #1", "4.2-4.3": "a #1", "4.4": "y", "4.4.3-4.4.4": "y", "62": "y"}, "bb": {"7": "y", "10": "y"}, "op_mob": {"10": "y", "11": "y", "11.1": "y", "11.5": "y", "12": "y", "12.1": "y", "37": "y"}, "and_chr": {"66": "y"}, "and_ff": {"57": "y"}, "ie_mob": {"10": "y #2", "11": "y #2"}, "and_uc": {"11.8": "y"}, "samsung": {"4": "y", "5": "y", "6.2": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "", "notes_by_num": {"1": "Partial support in Android 3 & 4 refers to not supporting masking.", "2": "IE9-11 desktop & mobile don't properly scale SVG files.  [Adding height, width, viewBox, and CSS rules](https://codepen.io/tomByrer/pen/qEBbzw?editors=110) seem to be the best workaround."}, "usage_perc_y": 97.67, "usage_perc_a": 0.28, "ucprefix": false, "parent": "", "keywords": "rect,circle,ellipse,line,polyline,polygon,defs,symbol,use,tspan,tref,textpath,stroke-dasharray,stroke-dashoffset", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}