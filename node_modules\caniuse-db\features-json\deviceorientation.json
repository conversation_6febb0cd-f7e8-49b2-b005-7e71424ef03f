{"title": "DeviceOrientation & DeviceMotion events", "description": "API for detecting orientation and motion events from the device running the browser.", "spec": "https://www.w3.org/TR/orientation-event/", "status": "cr", "links": [{"url": "https://www.html5rocks.com/en/tutorials/device/orientation/", "title": "HTML5 Rocks tutorial"}, {"url": "https://raw.github.com/phiggins42/has.js/master/detect/features.js#native-orientation", "title": "has.js test"}, {"url": "http://html5labs.interoperabilitybridges.com/prototypes/device-orientation-events/device-orientation-events/info", "title": "DeviceOrientation implementation prototype for IE10"}, {"url": "http://aurelio.audero.it/demo/device-orientation-api-demo.html", "title": "Demo"}], "bugs": [{"description": "`DeviceOrientationEvent.beta` has values between -90 and 90 on mobile Safari and between 180 and -180 on Firefox.\r\n`DeviceOrientationEvent.gamma` has values between -180 and 180 on mobile Safari and between 90 and -90 on Firefox.\r\nSee [Firefox reference](https://developer.mozilla.org/en-US/docs/Web/API/DeviceOrientationEvent)\r\nand [Safari reference](https://developer.apple.com/library/safari/documentation/SafariDOMAdditions/Reference/DeviceOrientationEventClassRef/DeviceOrientationEvent/DeviceOrientationEvent.html#//apple_ref/javascript/instp/DeviceOrientationEvent/beta)"}, {"description": "Safari on iOS doesn't implement the spec correctly, because alpha is arbitrary instead of relative to true north. Safari instead offers webkitCompassHeading`, which has the opposite sign to alpha and is also relative to magnetic north instead of true north. (see [details](https://github.com/w3c/deviceorientation/issues/6))"}], "categories": ["JS API"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "a #1"}, "edge": {"12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "p", "4": "p", "5": "p", "6": "a", "7": "a", "8": "a", "9": "a", "10": "a", "11": "a", "12": "a", "13": "a", "14": "a", "15": "a", "16": "a", "17": "a", "18": "a", "19": "a", "20": "a", "21": "a", "22": "a", "23": "a", "24": "a", "25": "a", "26": "a", "27": "a", "28": "a", "29": "a", "30": "a", "31": "a", "32": "a", "33": "a", "34": "a", "35": "a", "36": "a", "37": "a", "38": "a", "39": "a", "40": "a", "41": "a", "42": "a", "43": "a", "44": "a", "45": "a", "46": "a", "47": "a", "48": "a", "49": "a", "50": "a", "51": "a", "52": "a", "53": "a", "54": "a", "55": "a", "56": "a", "57": "a", "58": "a", "59": "a", "60": "a", "61": "a"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "a", "8": "a", "9": "a", "10": "a", "11": "a", "12": "a", "13": "a", "14": "a", "15": "a", "16": "a", "17": "a", "18": "a", "19": "a", "20": "a", "21": "a", "22": "a", "23": "a", "24": "a", "25": "a", "26": "a", "27": "a", "28": "a", "29": "a", "30": "a", "31": "a", "32": "a", "33": "a", "34": "a", "35": "a", "36": "a", "37": "a", "38": "a", "39": "a", "40": "a", "41": "a", "42": "a", "43": "a", "44": "a", "45": "a", "46": "a", "47": "a", "48": "a", "49": "a", "50": "a", "51": "a", "52": "a", "53": "a", "54": "a", "55": "a", "56": "a", "57": "a", "58": "a", "59": "a", "60": "a", "61": "a", "62": "a", "63": "a", "64": "a", "65": "a", "66": "a", "67": "a", "68": "a", "69": "a"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "n", "8": "n", "9": "n", "9.1": "n", "10": "n", "10.1": "n", "11": "n", "11.1": "n", "TP": "n"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "a", "16": "a", "17": "a", "18": "a", "19": "a", "20": "a", "21": "a", "22": "a", "23": "a", "24": "a", "25": "a", "26": "a", "27": "a", "28": "a", "29": "a", "30": "a", "31": "a", "32": "a", "33": "a", "34": "a", "35": "a", "36": "a", "37": "a", "38": "a", "39": "a", "40": "a", "41": "a", "42": "a", "43": "a", "44": "a", "45": "a", "46": "a", "47": "a", "48": "a", "49": "a", "50": "a", "51": "a", "52": "a"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "a", "5.0-5.1": "a", "6.0-6.1": "a", "7.0-7.1": "a", "8": "a", "8.1-8.4": "a", "9.0-9.2": "a", "9.3": "a", "10.0-10.2": "a", "10.3": "a", "11.0-11.2": "a", "11.3": "a"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "a", "4": "a", "4.1": "a", "4.2-4.3": "a", "4.4": "a", "4.4.3-4.4.4": "a", "62": "a"}, "bb": {"7": "n", "10": "a"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "y", "12.1": "y", "37": "a"}, "and_chr": {"66": "a"}, "and_ff": {"57": "a"}, "ie_mob": {"10": "n", "11": "y"}, "and_uc": {"11.8": "a"}, "samsung": {"4": "a", "5": "a", "6.2": "a"}, "and_qq": {"1.2": "a"}, "baidu": {"7.12": "a"}}, "notes": "Partial support refers to the lack of compassneedscalibration event. Partial support also refers to the lack of devicemotion event support for Chrome 30- and Opera. Opera Mobile 14 lost the ondevicemotion event support. Firefox 3.6, 4 and 5 support the non-standard [MozOrientation](https://developer.mozilla.org/en/DOM/MozOrientation) event.", "notes_by_num": {"1": "`compassneedscalibration` supported in IE11 only for compatible devices with Windows 8.1+."}, "usage_perc_y": 2.06, "usage_perc_a": 90.41, "ucprefix": false, "parent": "", "keywords": "ondeviceorientation,ondevicemotion", "ie_id": "deviceorientation,devicemotion", "chrome_id": "5874690627207168,5556931766779904", "firefox_id": "device-orientation", "webkit_id": "specification-deviceorientation-events", "shown": true}