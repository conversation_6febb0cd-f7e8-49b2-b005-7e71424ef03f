{"title": "XHTML+SMIL animation", "description": "Method of using SMIL animation in web pages", "spec": "https://www.w3.org/TR/XHTMLplusSMIL/", "status": "unoff", "links": [{"url": "https://en.wikipedia.org/wiki/XHTML%2BSMIL", "title": "Wikipedia"}, {"url": "http://leunen.me/fakesmile/", "title": "JS library to support XHTML+SMIL"}], "bugs": [], "categories": ["Other"], "stats": {"ie": {"5.5": "n", "6": "a", "7": "a", "8": "a", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n"}, "firefox": {"2": "p", "3": "p", "3.5": "p", "3.6": "p", "4": "p", "5": "p", "6": "p", "7": "p", "8": "p", "9": "p", "10": "p", "11": "p", "12": "p", "13": "p", "14": "p", "15": "p", "16": "p", "17": "p", "18": "p", "19": "p", "20": "p", "21": "p", "22": "p", "23": "p", "24": "p", "25": "p", "26": "p", "27": "p", "28": "p", "29": "p", "30": "p", "31": "p", "32": "p", "33": "p", "34": "p", "35": "p", "36": "p", "37": "p", "38": "p", "39": "p", "40": "p", "41": "p", "42": "p", "43": "p", "44": "p", "45": "p", "46": "p", "47": "p", "48": "p", "49": "p", "50": "p", "51": "p", "52": "p", "53": "p", "54": "p", "55": "p", "56": "p", "57": "p", "58": "p", "59": "p", "60": "p", "61": "p"}, "chrome": {"4": "p", "5": "p", "6": "p", "7": "p", "8": "p", "9": "p", "10": "p", "11": "p", "12": "p", "13": "p", "14": "p", "15": "p", "16": "p", "17": "p", "18": "p", "19": "p", "20": "p", "21": "p", "22": "p", "23": "p", "24": "p", "25": "p", "26": "p", "27": "p", "28": "p", "29": "p", "30": "p", "31": "p", "32": "p", "33": "p", "34": "p", "35": "p", "36": "p", "37": "p", "38": "p", "39": "p", "40": "p", "41": "p", "42": "p", "43": "p", "44": "p", "45": "p", "46": "p", "47": "p", "48": "p", "49": "p", "50": "p", "51": "p", "52": "p", "53": "p", "54": "p", "55": "p", "56": "p", "57": "p", "58": "p", "59": "p", "60": "p", "61": "p", "62": "p", "63": "p", "64": "p", "65": "p", "66": "p", "67": "p", "68": "p", "69": "p"}, "safari": {"3.1": "p", "3.2": "p", "4": "p", "5": "p", "5.1": "p", "6": "p", "6.1": "p", "7": "p", "7.1": "p", "8": "p", "9": "p", "9.1": "p", "10": "p", "10.1": "p", "11": "p", "11.1": "p", "TP": "p"}, "opera": {"9": "p", "9.5-9.6": "p", "10.0-10.1": "p", "10.5": "p", "10.6": "p", "11": "p", "11.1": "p", "11.5": "p", "11.6": "p", "12": "p", "12.1": "p", "15": "p", "16": "p", "17": "p", "18": "p", "19": "p", "20": "p", "21": "p", "22": "p", "23": "p", "24": "p", "25": "p", "26": "p", "27": "p", "28": "p", "29": "p", "30": "p", "31": "p", "32": "p", "33": "p", "34": "p", "35": "p", "36": "p", "37": "p", "38": "p", "39": "p", "40": "p", "41": "p", "42": "p", "43": "p", "44": "p", "45": "p", "46": "p", "47": "p", "48": "p", "49": "p", "50": "p", "51": "p", "52": "p"}, "ios_saf": {"3.2": "p", "4.0-4.1": "p", "4.2-4.3": "p", "5.0-5.1": "p", "6.0-6.1": "p", "7.0-7.1": "p", "8": "p", "8.1-8.4": "p", "9.0-9.2": "p", "9.3": "p", "10.0-10.2": "p", "10.3": "p", "11.0-11.2": "p", "11.3": "p"}, "op_mini": {"all": "p"}, "android": {"2.1": "p", "2.2": "p", "2.3": "p", "3": "p", "4": "p", "4.1": "p", "4.2-4.3": "p", "4.4": "p", "4.4.3-4.4.4": "p", "62": "p"}, "bb": {"7": "p", "10": "p"}, "op_mob": {"10": "p", "11": "p", "11.1": "p", "11.5": "p", "12": "p", "12.1": "p", "37": "p"}, "and_chr": {"66": "p"}, "and_ff": {"57": "p"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.8": "p"}, "samsung": {"4": "p", "5": "p", "6.2": "p"}, "and_qq": {"1.2": "p"}, "baidu": {"7.12": "p"}}, "notes": "Internet Explorer supports the W3C proposal HTML+TIME, which is largely the same as XHTML+SMIL", "notes_by_num": {}, "usage_perc_y": 0, "usage_perc_a": 0.2, "ucprefix": false, "parent": "xhtml", "keywords": "", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}