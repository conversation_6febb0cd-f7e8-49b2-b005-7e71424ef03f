{"title": "Basic console logging functions", "description": "Method of outputting data to the browser's console, intended for development purposes.", "spec": "https://console.spec.whatwg.org/", "status": "ls", "links": [{"url": "https://developer.mozilla.org/en-US/docs/Web/API/Console", "title": "MDN Web Docs - Console"}, {"url": "https://developer.chrome.com/devtools/docs/console-api", "title": "Chrome console reference"}, {"url": "https://msdn.microsoft.com/en-us/library/hh772169", "title": "Edge/Internet Explorer console reference"}], "bugs": [], "categories": ["JS API"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "a #1", "9": "a #1", "10": "y", "11": "y"}, "edge": {"12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "y", "5": "y", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y"}, "chrome": {"4": "y", "5": "y", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y", "63": "y", "64": "y", "65": "y", "66": "y", "67": "y", "68": "y", "69": "y"}, "safari": {"3.1": "y", "3.2": "y", "4": "y", "5": "y", "5.1": "y", "6": "y", "6.1": "y", "7": "y", "7.1": "y", "8": "y", "9": "y", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "11.1": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "y", "11.1": "y", "11.5": "y", "11.6": "y", "12": "y", "12.1": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y"}, "ios_saf": {"3.2": "y", "4.0-4.1": "y", "4.2-4.3": "y", "5.0-5.1": "y", "6.0-6.1": "y #3", "7.0-7.1": "y #3", "8": "y #3", "8.1-8.4": "y #3", "9.0-9.2": "y #3", "9.3": "y #3", "10.0-10.2": "y #3", "10.3": "y #3", "11.0-11.2": "y #3", "11.3": "y #3"}, "op_mini": {"all": "y #6"}, "android": {"2.1": "y #4", "2.2": "y #4", "2.3": "y #4", "3": "y #4", "4": "y #4", "4.1": "y #4", "4.2-4.3": "y #4", "4.4": "y #4", "4.4.3-4.4.4": "y #4", "62": "y #4"}, "bb": {"7": "n #2", "10": "n #2"}, "op_mob": {"10": "n", "11": "n #2", "11.1": "n #2", "11.5": "n #2", "12": "n #2", "12.1": "n #2", "37": "n #2"}, "and_chr": {"66": "y #4"}, "and_ff": {"57": "y #5"}, "ie_mob": {"10": "n #2", "11": "n #2"}, "and_uc": {"11.8": "n #2"}, "samsung": {"4": "y #4", "5": "y #4", "6.2": "y #4"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y #4"}}, "notes": "The basic functions that this information refers to include `console.log`, `console.info`, `console.warn`, `console.error`.", "notes_by_num": {"1": "Only supports console functions when developer tools are open, otherwise the `console` object is undefined and any calls will throw errors.", "2": "Allows `console` functions to be used without throwing errors, but does not appear to output the data anywhere.", "3": "Log output on iOS 6+ Safari can only be seen by connecting to a Mac and using the [Safari debugger](https://developer.apple.com/safari/tools/).", "4": "Log output on older Android browsers can be retrieved via Android's `logcat` command or using Chrome Developer Tools in Android 4.4+/Chrome for Android [see details](http://developer.android.com/guide/webapps/debugging.html)", "5": "Log output on Firefox for Android can be [accessed using WebIDE](https://developer.mozilla.org/en-US/docs/Tools/Remote_Debugging/Debugging_Firefox_for_Android_with_WebIDE)", "6": "See [this article](https://dev.opera.com/articles/opera-mini-and-javascript/) for details on how to see console logging in Opera Mini"}, "usage_perc_y": 89.98, "usage_perc_a": 0.29, "ucprefix": false, "parent": "", "keywords": "console.log,console.info,console.warn,console.error,window.console", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}