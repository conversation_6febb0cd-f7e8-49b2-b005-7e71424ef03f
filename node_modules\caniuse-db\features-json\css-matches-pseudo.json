{"title": ":matches() CSS pseudo-class", "description": "The `:matches()` (formerly `:any()`) pseudo-class checks whether the element at its position in the outer selector matches any of the selectors in its selector list. It's useful syntactic sugar that allows you to avoid writing out all the combinations manually as separate selectors. The effect is similar to nesting in Sass and most other CSS preprocessors.", "spec": "https://www.w3.org/TR/selectors4/#matches", "status": "wd", "links": [{"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/:any", "title": "MDN Web Docs - CSS :any"}, {"url": "https://webkit.org/blog/3615/css-selectors-inside-selectors-discover-matches-not-and-nth-child/", "title": "WebKit blog post about adding `:matches()` and other Selectors Level 4 features"}, {"url": "https://bugzilla.mozilla.org/show_bug.cgi?id=906353", "title": "Mozilla Bug 906353 - Add support for css4 selector :matches(), the standard of :-moz-any()"}, {"url": "https://wpdev.uservoice.com/forums/257854-microsoft-edge-developer/suggestions/9361350--matches", "title": "Microsoft Edge UserVoice feature request for :matches()"}, {"url": "http://output.jsbin.com/lehina", "title": "JS Bin testcase"}, {"url": "https://bugs.chromium.org/p/chromium/issues/detail?id=568705", "title": "Issue 568705: Chrome does not support :matches() selector"}], "bugs": [], "categories": ["CSS"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n"}, "firefox": {"2": "u", "3": "u", "3.5": "u", "3.6": "u", "4": "a x #3", "5": "a x #3", "6": "a x #3", "7": "a x #3", "8": "a x #3", "9": "a x #3", "10": "a x #3", "11": "a x #3", "12": "a x #3", "13": "a x #3", "14": "a x #3", "15": "a x #3", "16": "a x #3", "17": "a x #3", "18": "a x #3", "19": "a x #3", "20": "a x #3", "21": "a x #3", "22": "a x #3", "23": "a x #3", "24": "a x #3", "25": "a x #3", "26": "a x #3", "27": "a x #3", "28": "a x #3", "29": "a x #3", "30": "a x #3", "31": "a x #3", "32": "a x #3", "33": "a x #3", "34": "a x #3", "35": "a x #3", "36": "a x #3", "37": "a x #3", "38": "a x #3", "39": "a x #3", "40": "a x #3", "41": "a x #3", "42": "a x #3", "43": "a x #3", "44": "a x #3", "45": "a x #3", "46": "a x #3", "47": "a x #3", "48": "a x #3", "49": "a x #3", "50": "a x #3", "51": "a x #3", "52": "a x #3", "53": "a x #3", "54": "a x #3", "55": "a x #3", "56": "a x #3", "57": "a x #3", "58": "a x #3", "59": "a x #3", "60": "a x #3", "61": "a x #3"}, "chrome": {"4": "u", "5": "u", "6": "u", "7": "u", "8": "u", "9": "u", "10": "u", "11": "u", "12": "u", "13": "u", "14": "u", "15": "a x #1", "16": "a x #1", "17": "a x #1", "18": "a x #1", "19": "a x #1", "20": "a x #1", "21": "a x #1", "22": "a x #1", "23": "a x #1", "24": "a x #1", "25": "a x #1", "26": "a x #1", "27": "a x #1", "28": "a x #1", "29": "a x #1", "30": "a x #1", "31": "a x #1", "32": "a x #1", "33": "a x #1", "34": "a x #1", "35": "a x #1", "36": "a x #1", "37": "a x #1", "38": "a x #1", "39": "a x #1", "40": "a x #1", "41": "a x #1", "42": "a x #1", "43": "a x #1", "44": "a x #1", "45": "a x #1", "46": "a x #1", "47": "a x #1", "48": "a x #1", "49": "a x #1", "50": "a x #1", "51": "a x #1", "52": "a x #1", "53": "a x #1", "54": "a x #1", "55": "a x #1", "56": "a x #1", "57": "a x #1", "58": "a x #1", "59": "a x #1", "60": "a x #1", "61": "a x #1", "62": "a x #1", "63": "a x #1", "64": "a x #1", "65": "a x #1", "66": "a x #1", "67": "a x #1", "68": "a x #1", "69": "a x #1"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "u", "5.1": "a x #1", "6": "a x #1", "6.1": "a x #1", "7": "a x #1", "7.1": "a x #1", "8": "a x #1", "9": "y #2", "9.1": "y #2", "10": "y #2", "10.1": "y #2", "11": "y #2", "11.1": "y #2", "TP": "y #2"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "a x #1", "16": "a x #1", "17": "a x #1", "18": "a x #1", "19": "a x #1", "20": "a x #1", "21": "a x #1", "22": "a x #1", "23": "a x #1", "24": "a x #1", "25": "a x #1", "26": "a x #1", "27": "a x #1", "28": "a x #1", "29": "a x #1", "30": "a x #1", "31": "a x #1", "32": "a x #1", "33": "a x #1", "34": "a x #1", "35": "a x #1", "36": "a x #1", "37": "a x #1", "38": "a x #1", "39": "a x #1", "40": "a x #1", "41": "a x #1", "42": "a x #1", "43": "a x #1", "44": "a x #1", "45": "a x #1", "46": "a x #1", "47": "a x #1", "48": "a x #1", "49": "a x #1", "50": "a x #1", "51": "a x #1", "52": "a x #1"}, "ios_saf": {"3.2": "u", "4.0-4.1": "u", "4.2-4.3": "u", "5.0-5.1": "u", "6.0-6.1": "u", "7.0-7.1": "a x #1", "8": "a x #1", "8.1-8.4": "a x #1", "9.0-9.2": "y #2", "9.3": "y #2", "10.0-10.2": "y #2", "10.3": "y #2", "11.0-11.2": "y #2", "11.3": "y #2"}, "op_mini": {"all": "n"}, "android": {"2.1": "u", "2.2": "u", "2.3": "u", "3": "u", "4": "a x #1", "4.1": "a x #1", "4.2-4.3": "a x #1", "4.4": "a x #1", "4.4.3-4.4.4": "a x #1", "62": "a x #1"}, "bb": {"7": "u", "10": "a x #1"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "a x #1"}, "and_chr": {"66": "a x #1"}, "and_ff": {"57": "a x #3"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.8": "a x #1"}, "samsung": {"4": "a x #1", "5": "a x #1", "6.2": "a x #1"}, "and_qq": {"1.2": "a x #1"}, "baidu": {"7.12": "a x #1"}}, "notes": "Most browsers support this spelled as a prefixed `:-vendor-any()` pseudo-class.", "notes_by_num": {"1": "Only supports the `:-webkit-any()` pseudo-class, which is deprecated due to handling specificity incorrectly.", "2": "Also supports the `:-webkit-any()` pseudo-class, which is deprecated due to handling specificity incorrectly.", "3": "Only supports the `:-moz-any()` pseudo-class."}, "usage_perc_y": 12.73, "usage_perc_a": 77.19, "ucprefix": false, "parent": "", "keywords": ":matches,matches,:any,any", "ie_id": "", "chrome_id": "5445716612743168", "firefox_id": "", "webkit_id": "feature-css-selector-:matches()", "shown": true}