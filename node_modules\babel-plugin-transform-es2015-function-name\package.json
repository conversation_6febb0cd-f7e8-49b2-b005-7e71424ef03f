{"name": "babel-plugin-transform-es2015-function-name", "version": "6.24.1", "description": "Apply ES2015 function.name semantics to all functions", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-es2015-function-name", "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"babel-helper-function-name": "^6.24.1", "babel-types": "^6.24.1", "babel-runtime": "^6.22.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.24.1"}}