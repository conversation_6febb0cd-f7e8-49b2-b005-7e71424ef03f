{"title": "Media Source Extensions", "description": "API allowing media data to be accessed from HTML `video` and `audio` elements.", "spec": "https://www.w3.org/TR/media-source/", "status": "cr", "links": [{"url": "https://developer.mozilla.org/en-US/docs/Web/API/MediaSource#Browser_compatibility", "title": "MDN Web Docs - MediaSource"}, {"url": "https://msdn.microsoft.com/en-us/library/dn594470%28v=vs.85%29.aspx", "title": "MSDN article"}, {"url": "http://html5-demos.appspot.com/static/media-source.html", "title": "MediaSource demo"}], "bugs": [], "categories": ["DOM", "JS API"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "a #2"}, "edge": {"12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n d #1", "26": "n d #1", "27": "n d #1", "28": "n d #1", "29": "n d #1", "30": "n d #1", "31": "n d #1", "32": "n d #1", "33": "n d #1", "34": "n d #1", "35": "n d #1", "36": "n d #1", "37": "n d #1", "38": "n d #1", "39": "n d #1", "40": "n d #1", "41": "n d #1", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n d", "18": "n d", "19": "n d", "20": "n d", "21": "n d", "22": "n d", "23": "y x", "24": "y x", "25": "y x", "26": "y x", "27": "y x", "28": "y x", "29": "y x", "30": "y x", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y", "63": "y", "64": "y", "65": "y", "66": "y", "67": "y", "68": "y", "69": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "n", "8": "y", "9": "y", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "11.1": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "n", "8.1-8.4": "n", "9.0-9.2": "n", "9.3": "n", "10.0-10.2": "n", "10.3": "n", "11.0-11.2": "n", "11.3": "n"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "y", "62": "y"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "y"}, "and_chr": {"66": "y"}, "and_ff": {"57": "y"}, "ie_mob": {"10": "n", "11": "y"}, "and_uc": {"11.8": "y"}, "samsung": {"4": "n #3", "5": "y", "6.2": "y"}, "and_qq": {"1.2": "n"}, "baidu": {"7.12": "y"}}, "notes": "", "notes_by_num": {"1": "Requires the `media.mediasource.enabled` flag to be enabled, support is limited to a whitelist including the YouTube, Netflix, and Dailymotion websites", "2": "Partial support in IE11 refers to only working in Windows 8+", "3": "Due to compatibility issues, MediaSource Extensions are currently disabled by default in Samsung Internet."}, "usage_perc_y": 79, "usage_perc_a": 2.76, "ucprefix": false, "parent": "", "keywords": "sourcebuffer,endofstream,mse", "ie_id": "mediasourceextensions", "chrome_id": "4563797888991232", "firefox_id": "mse", "webkit_id": "", "shown": true}