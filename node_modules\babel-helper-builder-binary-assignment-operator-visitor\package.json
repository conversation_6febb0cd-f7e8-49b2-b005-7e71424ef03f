{"name": "babel-helper-builder-binary-assignment-operator-visitor", "version": "6.24.1", "description": "Helper function to build binary assignment operator visitors", "repository": "https://github.com/babel/babel/tree/master/packages/babel-helper-builder-binary-assignment-operator-visitor", "license": "MIT", "main": "lib/index.js", "dependencies": {"babel-helper-explode-assignable-expression": "^6.24.1", "babel-runtime": "^6.22.0", "babel-types": "^6.24.1"}}