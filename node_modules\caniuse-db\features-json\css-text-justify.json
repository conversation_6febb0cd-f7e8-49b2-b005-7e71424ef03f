{"title": "CSS text-justify", "description": "CSS property to define how text should be justified when `text-align: justify` is set.", "spec": "https://drafts.csswg.org/css-text-3/#text-justify-property", "status": "wd", "links": [{"url": "https://bugs.chromium.org/p/chromium/issues/detail?id=248894", "title": "Chrome support bug"}, {"url": "https://bugs.webkit.org/show_bug.cgi?id=99945", "title": "WebKit support bug"}, {"url": "https://bugzilla.mozilla.org/show_bug.cgi?id=276079", "title": "Firefox support bug"}], "bugs": [], "categories": ["CSS"], "stats": {"ie": {"5.5": "u", "6": "u", "7": "u", "8": "a #1", "9": "a #1", "10": "a #1", "11": "a #1"}, "edge": {"12": "a #1", "13": "a #1", "14": "a #1", "15": "a #1", "16": "a #1", "17": "a #1", "18": "a #1"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n", "46": "n", "47": "n", "48": "n", "49": "n", "50": "n", "51": "n", "52": "n", "53": "n", "54": "n d #3 #4", "55": "y #4", "56": "y #4", "57": "y #4", "58": "y #4", "59": "y #4", "60": "y #4", "61": "y #4"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n d #2", "44": "n d #2", "45": "n d #2", "46": "n d #2", "47": "n d #2", "48": "n d #2", "49": "n d #2", "50": "n d #2", "51": "n d #2", "52": "n d #2", "53": "n d #2", "54": "n d #2", "55": "n d #2", "56": "n d #2", "57": "n d #2", "58": "n d #2", "59": "n d #2", "60": "n d #2", "61": "n d #2", "62": "n d #2", "63": "n d #2", "64": "n d #2", "65": "n d #2", "66": "n d #2", "67": "n d #2", "68": "n d #2", "69": "n d #2"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "n", "8": "n", "9": "n", "9.1": "n", "10": "n", "10.1": "n", "11": "n", "11.1": "n", "TP": "n"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n d #2", "31": "n d #2", "32": "n d #2", "33": "n d #2", "34": "n d #2", "35": "n d #2", "36": "n d #2", "37": "n d #2", "38": "n d #2", "39": "n d #2", "40": "n d #2", "41": "n d #2", "42": "n d #2", "43": "n d #2", "44": "n d #2", "45": "n d #2", "46": "n d #2", "47": "n d #2", "48": "n d #2", "49": "n d #2", "50": "n d #2", "51": "n d #2", "52": "n d #2"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "n", "8.1-8.4": "n", "9.0-9.2": "n", "9.3": "n", "10.0-10.2": "n", "10.3": "n", "11.0-11.2": "n", "11.3": "n"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "62": "n d #2"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "n d #2"}, "and_chr": {"66": "n d #2"}, "and_ff": {"57": "y #4"}, "ie_mob": {"10": "a #1", "11": "a #1"}, "and_uc": {"11.8": "n"}, "samsung": {"4": "n", "5": "n d #2", "6.2": "n d #2"}, "and_qq": {"1.2": "n d #2"}, "baidu": {"7.12": "n d #2"}}, "notes": "", "notes_by_num": {"1": "Supports `inter-word`, but not `inter-character` or  `none`. Also supports the following unofficial values: `distribute` , `distribute-all-lines`, `distribute-center-last`, `inter-cluster`, `inter-ideograph`, `newspaper`. See [MSDN](https://msdn.microsoft.com/en-us/library/ms531172%28v=vs.85%29.aspx) for details.", "2": "`inter-word` and `distribute` values supported behind the \"Experimental platform features\" flag but `distribute` support [is buggy](https://bugs.chromium.org/p/chromium/issues/detail?id=467406)", "3": "Behind the \"layout.css.text-justify.enabled\" flag", "4": "Supports `auto`, `none`, `inter-word`, `inter-character`, and `distribute` with the exact same meaning and behavior as `inter-character` for legacy reasons."}, "usage_perc_y": 4.12, "usage_perc_a": 5.26, "ucprefix": false, "parent": "", "keywords": "", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}