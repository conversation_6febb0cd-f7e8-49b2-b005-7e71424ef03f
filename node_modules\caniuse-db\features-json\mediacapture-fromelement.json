{"title": "Media Capture from DOM Elements API", "description": "API to capture Real-Time video and audio from a DOM element, such as a `<video>`, `<audio>`, or `<canvas>` element via the `captureStream` method, in the form of a `MediaStream`", "spec": "https://w3c.github.io/mediacapture-fromelement/", "status": "wd", "links": [{"url": "https://developer.mozilla.org/en-US/docs/Web/API/HTMLCanvasElement/captureStream", "title": "MDN Web Docs - capture from <canvas>"}, {"url": "https://developer.mozilla.org/en-US/docs/Web/API/HTMLMediaElement/captureStream", "title": "MDN Web Docs - capture from <video>/<audio>"}, {"url": "https://developers.google.com/web/updates/2016/10/capture-stream", "title": "Google Developers article"}], "bugs": [], "categories": ["DOM", "JS API"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "a #2", "44": "a #2", "45": "a #2", "46": "a #2", "47": "a #2", "48": "a #2", "49": "a #2", "50": "a #2", "51": "a #2", "52": "a #2", "53": "a #2", "54": "a #2", "55": "a #2", "56": "a #2", "57": "a #2", "58": "a #2", "59": "a #2", "60": "a #2", "61": "a #2"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n", "46": "n", "47": "n", "48": "n", "49": "n", "50": "n", "51": "a d #2", "52": "a d #2", "53": "a d #2", "54": "a d #2", "55": "a d #2", "56": "a d #2", "57": "a d #2", "58": "a d #2", "59": "a d #2", "60": "a d #2", "61": "a d #2", "62": "y", "63": "y", "64": "y", "65": "y", "66": "y", "67": "y", "68": "y", "69": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "n", "8": "n", "9": "n", "9.1": "n", "10": "n", "10.1": "n", "11": "a #1", "11.1": "a #1", "TP": "a #1"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "a d #2", "37": "a d #2", "38": "a d #2", "39": "a d #2", "40": "a d #2", "41": "a d #2", "42": "a d #2", "43": "a d #2", "44": "a d #2", "45": "a d #2", "46": "a d #2", "47": "a d #2", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "n", "8.1-8.4": "n", "9.0-9.2": "n", "9.3": "n", "10.0-10.2": "n", "10.3": "n", "11.0-11.2": "n", "11.3": "n"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "62": "y"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "n"}, "and_chr": {"66": "y"}, "and_ff": {"57": "a #2"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.8": "a #1"}, "samsung": {"4": "n", "5": "a #1", "6.2": "a #1"}, "and_qq": {"1.2": "a #1"}, "baidu": {"7.12": "n"}}, "notes": "", "notes_by_num": {"1": "Does not support capture from `<video>`/`<audio>`", "2": "Capture from `<video>`/`<audio>` can be enabled via the Experimental Web Platform Features flag."}, "usage_perc_y": 56.33, "usage_perc_a": 17.63, "ucprefix": false, "parent": "stream", "keywords": "canvas,mediastream,capturestream,canvas.capturestream", "ie_id": "", "chrome_id": "5522768674160640,4817998447640576", "firefox_id": "", "webkit_id": "", "shown": true}