{"title": "WebGL - 3D Canvas graphics", "description": "Method of generating dynamic 3D graphics using JavaScript, accelerated through hardware", "spec": "https://www.khronos.org/registry/webgl/specs/1.0/", "status": "other", "links": [{"url": "http://khronos.org/webgl/wiki/Getting_a_WebGL_Implementation", "title": "Instructions on enabling WebGL"}, {"url": "http://www.khronos.org/webgl/wiki/Tutorial", "title": "Tutorial"}, {"url": "https://hacks.mozilla.org/2009/12/webgl-draft-released-today/", "title": "Firefox blog post"}, {"url": "http://webkit.org/blog/603/webgl-now-available-in-webkit-nightlies/", "title": "WebKit blog post"}, {"url": "https://github.com/iewebgl/iewebgl", "title": "Polyfill for IE"}], "bugs": [{"description": "Older versions of IE11 have only [partial support of the spec](http://connect.microsoft.com/IE/feedback/details/795172/ie11-fails-more-than-half-tests-in-official-webgl-conformance-test-suite), though it's much better with the latest update."}], "categories": ["<PERSON><PERSON>"], "stats": {"ie": {"5.5": "n", "6": "p", "7": "p", "8": "p", "9": "p", "10": "p", "11": "y #1"}, "edge": {"12": "y #1", "13": "y #1", "14": "y #1", "15": "y #1", "16": "y #1", "17": "y #1", "18": "y #1"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "y #1", "5": "y #1", "6": "y #1", "7": "y #1", "8": "y #1", "9": "y #1", "10": "y #1", "11": "y #1", "12": "y #1", "13": "y #1", "14": "y #1", "15": "y #1", "16": "y #1", "17": "y #1", "18": "y #1", "19": "y #1", "20": "y #1", "21": "y #1", "22": "y #1", "23": "y #1", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "y #1", "9": "y #1", "10": "y #1", "11": "y #1", "12": "y #1", "13": "y #1", "14": "y #1", "15": "y #1", "16": "y #1", "17": "y #1", "18": "y #1", "19": "y #1", "20": "y #1", "21": "y #1", "22": "y #1", "23": "y #1", "24": "y #1", "25": "y #1", "26": "y #1", "27": "y #1", "28": "y #1", "29": "y #1", "30": "y #1", "31": "y #1", "32": "y #1", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y", "63": "y", "64": "y", "65": "y", "66": "y", "67": "y", "68": "y", "69": "y"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "y #1", "6": "y #1", "6.1": "y #1", "7": "y #1", "7.1": "y #1", "8": "y", "9": "y", "9.1": "y", "10": "y", "10.1": "y", "11": "y", "11.1": "y", "TP": "y"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "y #1", "12.1": "y #1", "15": "y #1", "16": "y #1", "17": "y #1", "18": "y #1", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "y", "8.1-8.4": "y", "9.0-9.2": "y", "9.3": "y", "10.0-10.2": "y", "10.3": "y", "11.0-11.2": "y", "11.3": "y"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "62": "y"}, "bb": {"7": "n", "10": "y"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "y", "12.1": "y", "37": "y"}, "and_chr": {"66": "y"}, "and_ff": {"57": "y"}, "ie_mob": {"10": "p", "11": "y #1"}, "and_uc": {"11.8": "y #1"}, "samsung": {"4": "y", "5": "y", "6.2": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "WebGL support is dependent on GPU support and may not be available on older devices. This is due to the additional requirement for users to have [up to date video drivers](http://www.khronos.org/webgl/wiki/BlacklistsAndWhitelists).\r\n\r\nNote that WebGL is part of the [Khronos Group](http://www.khronos.org/webgl/), not the W3C.", "notes_by_num": {"1": "WebGL context is accessed from \"experimental-webgl\" rather than \"webgl\""}, "usage_perc_y": 93.58, "usage_perc_a": 0, "ucprefix": false, "parent": "canvas", "keywords": "web gl", "ie_id": "webglcanvas3d,webglinstancingextension", "chrome_id": "6049512976023552", "firefox_id": "webgl-1", "webkit_id": "specification-webgl-1", "shown": true}