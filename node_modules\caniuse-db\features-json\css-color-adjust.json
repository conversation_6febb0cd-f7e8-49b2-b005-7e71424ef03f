{"title": "CSS color-adjust", "description": "The `color-adjust` (or `-webkit-print-color-adjust` as prefixed in WebKit/Blink browsers) property is a non-standard CSS extension that can be used to force printing of background colors and images.", "spec": "https://drafts.csswg.org/css-color-4/#color-adjust", "status": "unoff", "links": [{"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/-webkit-print-color-adjust", "title": "MDN web docs - -webkit-print-color-adjust"}, {"url": "https://developer.microsoft.com/en-us/microsoft-edge/platform/issues/12399195/", "title": "Edge issue with color-adjust"}, {"url": "https://bugs.chromium.org/p/chromium/issues/detail?id=131054", "title": "Chromium bug with color-adjust property"}, {"url": "https://codepen.io/y<PERSON><PERSON><PERSON><PERSON><PERSON>/pen/XEpJLr", "title": "Codepen demo of color-adjust usage"}], "bugs": [{"description": "Chrome and Safari do not print backgrounds of the `<body>` element. If this property is set to exact for the `<body>` element, it will apply only to its descendants. [test case](https://jsfiddle.net/soul_wish/8tw09dd0/)"}], "categories": ["CSS"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "u"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n", "46": "n", "47": "n", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y"}, "chrome": {"4": "u", "5": "u", "6": "u", "7": "u", "8": "u", "9": "u", "10": "u", "11": "u", "12": "u", "13": "u", "14": "u", "15": "u", "16": "u", "17": "u", "18": "u", "19": "y x", "20": "y x", "21": "y x", "22": "y x", "23": "y x", "24": "y x", "25": "y x", "26": "y x", "27": "y x", "28": "y x", "29": "y x", "30": "y x", "31": "y x", "32": "y x", "33": "y x", "34": "y x", "35": "y x", "36": "y x", "37": "y x", "38": "y x", "39": "y x", "40": "y x", "41": "y x", "42": "y x", "43": "y x", "44": "y x", "45": "y x", "46": "y x", "47": "y x", "48": "y x", "49": "y x", "50": "y x", "51": "y x", "52": "y x", "53": "y x", "54": "y x", "55": "y x", "56": "y x", "57": "y x", "58": "y x", "59": "y x", "60": "y x", "61": "y x", "62": "y x", "63": "y x", "64": "y x", "65": "y x", "66": "y x", "67": "y x", "68": "y x", "69": "y x"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "y x", "6.1": "y x", "7": "y x", "7.1": "y x", "8": "y x", "9": "y x", "9.1": "y x", "10": "y x", "10.1": "y x", "11": "y x", "11.1": "y x", "TP": "y x"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "y x", "16": "y x", "17": "y x", "18": "y x", "19": "y x", "20": "y x", "21": "y x", "22": "y x", "23": "y x", "24": "y x", "25": "y x", "26": "y x", "27": "y x", "28": "y x", "29": "y x", "30": "y x", "31": "y x", "32": "y x", "33": "y x", "34": "y x", "35": "y x", "36": "y x", "37": "y x", "38": "y x", "39": "y x", "40": "y x", "41": "y x", "42": "y x", "43": "y x", "44": "y x", "45": "y x", "46": "y x", "47": "y x", "48": "y x", "49": "y x", "50": "y x", "51": "y x", "52": "y x"}, "ios_saf": {"3.2": "u", "4.0-4.1": "u", "4.2-4.3": "u", "5.0-5.1": "u", "6.0-6.1": "u", "7.0-7.1": "u", "8": "u", "8.1-8.4": "u", "9.0-9.2": "u", "9.3": "u", "10.0-10.2": "u", "10.3": "u", "11.0-11.2": "u", "11.3": "u"}, "op_mini": {"all": "n"}, "android": {"2.1": "u", "2.2": "u", "2.3": "u", "3": "u", "4": "u", "4.1": "u", "4.2-4.3": "u", "4.4": "u", "4.4.3-4.4.4": "u", "62": "u"}, "bb": {"7": "u", "10": "u"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "n"}, "and_chr": {"66": "u"}, "and_ff": {"57": "n"}, "ie_mob": {"10": "u", "11": "u"}, "and_uc": {"11.8": "u"}, "samsung": {"4": "u", "5": "u", "6.2": "u"}, "and_qq": {"1.2": "u"}, "baidu": {"7.12": "u"}}, "notes": "", "notes_by_num": {}, "usage_perc_y": 36.31, "usage_perc_a": 0, "ucprefix": false, "parent": "", "keywords": "color-adjust,print-color-adjust,color adjust,print color adjust,coloradjust,printcoloradjust", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}