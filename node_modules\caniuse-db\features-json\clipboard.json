{"title": "Clipboard API", "description": "API to provide copy, cut and paste events as well as provide access to the OS clipboard.", "spec": "https://www.w3.org/TR/clipboard-apis/", "status": "wd", "links": [{"url": "https://developer.mozilla.org/en-US/docs/Web/API/ClipboardEvent", "title": "MDN Web Docs - ClipboardEvent"}, {"url": "https://www.lucidchart.com/techblog/2014/12/02/definitive-guide-copying-pasting-javascript/", "title": "Guide on cross-platform clipboard access"}], "bugs": [{"description": "Before Firefox 41, `queryCommandEnabled` and `execCommand` with arguments `cut`, `copy` or `paste` would throw errors instead of return `false`."}], "categories": ["JS API"], "stats": {"ie": {"5.5": "a #1 #2 #5", "6": "a #1 #2 #5", "7": "a #1 #2 #5", "8": "a #1 #2 #5", "9": "a #1 #2 #5", "10": "a #1 #2 #5", "11": "a #1 #2 #5"}, "edge": {"12": "a #1 #2 #5", "13": "a #1 #2 #5", "14": "a #1 #2 #5", "15": "a #1 #2 #5", "16": "a #1 #2 #5", "17": "a #2", "18": "a #2"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "a #2 #3", "23": "a #2 #3", "24": "a #2 #3", "25": "a #2 #3", "26": "a #2 #3", "27": "a #2 #3", "28": "a #2 #3", "29": "a #2 #3", "30": "a #2 #3", "31": "a #2 #3", "32": "a #2 #3", "33": "a #2 #3", "34": "a #2 #3", "35": "a #2 #3", "36": "a #2 #3", "37": "a #2 #3", "38": "a #2 #3", "39": "a #2 #3", "40": "a #2 #3", "41": "a #6", "42": "a #6", "43": "a #6", "44": "a #6", "45": "a #6", "46": "a #6", "47": "a #6", "48": "a #6", "49": "a #6", "50": "a #6", "51": "a #6", "52": "a #6", "53": "a #6", "54": "a #6", "55": "a #6", "56": "a #6", "57": "a #6", "58": "a #6", "59": "a #6", "60": "a #6", "61": "a #6"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "a #3 #5", "14": "a #3 #5", "15": "a #3 #5", "16": "a #3 #5", "17": "a #3 #5", "18": "a #3 #5", "19": "a #3 #5", "20": "a #3 #5", "21": "a #3 #5", "22": "a #3 #5", "23": "a #3 #5", "24": "a #3 #5", "25": "a #3 #5", "26": "a #3 #5", "27": "a #3 #5", "28": "a #3 #5", "29": "a #3 #5", "30": "a #3 #5", "31": "a #3 #5", "32": "a #3 #5", "33": "a #3 #5", "34": "a #3 #5", "35": "a #3 #5", "36": "a #3 #5", "37": "a #3 #5", "38": "a #3 #5", "39": "a #3 #5", "40": "a #3 #5", "41": "a #3 #5", "42": "a #3 #5", "43": "a #5 #7", "44": "a #5 #7", "45": "a #5 #7", "46": "a #5 #7", "47": "a #5 #7", "48": "a #5 #7", "49": "a #5 #7", "50": "a #5 #7", "51": "a #5 #7", "52": "a #5 #7", "53": "a #5 #7", "54": "a #5 #7", "55": "a #5 #7", "56": "a #5 #7", "57": "a #5 #7", "58": "a #5 #7", "59": "a #5 #7", "60": "a #5 #7", "61": "a #5 #7", "62": "a #5 #7", "63": "a #5 #7", "64": "a #5 #7", "65": "a #5 #7", "66": "a #5 #7", "67": "a #5 #7", "68": "a #5 #7", "69": "a #5 #7"}, "safari": {"3.1": "u", "3.2": "u", "4": "a #2 #3 #5", "5": "a #2 #3 #5", "5.1": "a #2 #3 #5", "6": "a #2 #3 #5", "6.1": "a #2 #3 #5", "7": "a #2 #3 #5", "7.1": "a #2 #3 #5", "8": "a #2 #3 #5", "9": "a #2 #3 #5", "9.1": "a #2 #3 #5", "10": "a #2 #5", "10.1": "a #2 #5", "11": "a #2 #5", "11.1": "a #2 #5", "TP": "a #2 #5"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "u", "12.1": "a #3", "15": "a #3 #5", "16": "a #3 #5", "17": "a #3 #5", "18": "a #3 #5", "19": "a #3 #5", "20": "a #3 #5", "21": "a #3 #5", "22": "a #3 #5", "23": "a #3 #5", "24": "a #3 #5", "25": "a #3 #5", "26": "a #3 #5", "27": "a #3 #5", "28": "a #3 #5", "29": "a #3 #5", "30": "a #5 #7", "31": "a #5 #7", "32": "a #5 #7", "33": "a #5 #7", "34": "a #5 #7", "35": "a #5 #7", "36": "a #5 #7", "37": "a #5 #7", "38": "a #5 #7", "39": "a #5 #7", "40": "a #5 #7", "41": "a #5 #7", "42": "a #5 #7", "43": "a #5 #7", "44": "a #5 #7", "45": "a #5 #7", "46": "a #5 #7", "47": "a #5 #7", "48": "a #5 #7", "49": "a #5 #7", "50": "a #5 #7", "51": "a #5 #7", "52": "a #5 #7"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "a #2 #3 #5", "6.0-6.1": "a #2 #3 #5", "7.0-7.1": "a #2 #3 #5", "8": "a #2 #3 #5", "8.1-8.4": "a #2 #3 #5", "9.0-9.2": "a #2 #3 #5", "9.3": "a #2 #3 #5", "10.0-10.2": "a #2 #3 #5", "10.3": "a #2 #3 #5", "11.0-11.2": "a #2 #3 #5", "11.3": "a #2 #3 #5"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "a #2 #5", "4.4.3-4.4.4": "a #2 #5", "62": "a #2 #5"}, "bb": {"7": "n", "10": "a #2 #5"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "u", "37": "a #4 #5"}, "and_chr": {"66": "a #5"}, "and_ff": {"57": "a #4"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.8": "n"}, "samsung": {"4": "a #2 #5", "5": "a #5", "6.2": "a #5"}, "and_qq": {"1.2": "a #5 #7"}, "baidu": {"7.12": "a #5"}}, "notes": "Internet Explorer will display a security prompt for access to the OS clipboard.\r\n\r\nChrome 42+, Opera 29+ and Firefox 41+ support clipboard reading/writing only when part of a user action (click, keydown, etc).\r\n\r\nFirefox 40- users [can enable support](https://developer.mozilla.org/en-US/docs/Midas/Security_preferences) with a security preference setting.", "notes_by_num": {"1": "Only supports `Text` and `URL` data types and uses [a non-standard method](http://msdn.microsoft.com/en-us/library/ie/ms535220%28v=vs.85%29.aspx) of interacting with the clipboard.", "2": "Only fires `copy` event on a valid selection and only `cut` and `paste` in focused editable fields.", "3": "Only supports OS clipboard reading/writing via shortcut keys, not through `document.execCommand()`.", "4": "Only supports `paste` event (on focused editable field).", "5": "Does not support the `ClipboardEvent` constructor", "6": "Supports `cut` & `copy` events without a focused editable field, but not `paste` (presumably for security reasons)", "7": "Supports `cut` & `copy` events without a focused editable field, but does not fire `paste` with `document.execCommand('paste')`  "}, "usage_perc_y": 0, "usage_perc_a": 87.21, "ucprefix": false, "parent": "", "keywords": "cut,copy,paste,clipboarddata,clipboardevent", "ie_id": "clipboardapi", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}