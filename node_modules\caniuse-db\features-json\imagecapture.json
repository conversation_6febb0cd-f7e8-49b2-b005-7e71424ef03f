{"title": "ImageCapture API", "description": "The Image Capture API provides access to the Video Camera for taking photos while configuring picture-specific settings such as e.g. zoom or auto focus metering area.", "spec": "https://w3c.github.io/mediacapture-image/", "status": "wd", "links": [{"url": "https://codepen.io/miguelao/pen/ZOkOQw", "title": "Minimal code pen"}, {"url": "https://rawgit.com/Miguelao/demos/master/imagecapture.html", "title": "Extended demo"}, {"url": "https://bugzilla.mozilla.org/show_bug.cgi?id=916643", "title": "Firefox tracking bug"}, {"url": "https://bugs.chromium.org/p/chromium/issues/detail?id=518807", "title": "Chromium tracking bug"}], "bugs": [], "categories": ["DOM", "JS API"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n d #1", "36": "n d #1", "37": "n d #1", "38": "n d #1", "39": "n d #1", "40": "n d #1", "41": "n d #1", "42": "n d #1", "43": "n d #1", "44": "n d #1", "45": "n d #1", "46": "n d #1", "47": "n d #1", "48": "n d #1", "49": "n d #1", "50": "n d #1", "51": "n d #1", "52": "n d #1", "53": "n d #1", "54": "n d #1", "55": "n d #1", "56": "n d #1", "57": "n d #1", "58": "n d #1", "59": "n d #1", "60": "n d #1", "61": "n d #1"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n", "46": "n", "47": "n", "48": "n", "49": "n", "50": "n", "51": "n", "52": "n", "53": "n d #2", "54": "n d #2", "55": "n d #2", "56": "n d #2", "57": "n d #2", "58": "n d #2", "59": "n d #2", "60": "n d #2", "61": "n d #2", "62": "n d #2", "63": "n d #2", "64": "n d #2", "65": "n d #2", "66": "n d #2", "67": "n d #2", "68": "n d #2", "69": "n d #2"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "n", "8": "n", "9": "n", "9.1": "n", "10": "n", "10.1": "n", "11": "n", "11.1": "n", "TP": "n"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n d #2", "41": "n d #2", "42": "n d #2", "43": "n d #2", "44": "n d #2", "45": "n d #2", "46": "n d #2", "47": "n d #2", "48": "n d #2", "49": "n d #2", "50": "n d #2", "51": "n d #2", "52": "n d #2"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "n", "8.1-8.4": "n", "9.0-9.2": "n", "9.3": "n", "10.0-10.2": "n", "10.3": "n", "11.0-11.2": "n", "11.3": "n"}, "op_mini": {"all": "n"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "62": "n"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "n"}, "and_chr": {"66": "y"}, "and_ff": {"57": "n"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.8": "n"}, "samsung": {"4": "n", "5": "y", "6.2": "y"}, "and_qq": {"1.2": "n d #2"}, "baidu": {"7.12": "y"}}, "notes": "Firefox supports the `takePhoto()` method only.", "notes_by_num": {"1": "Can be enabled via the about:config entry dom.imagecapture.enabled.", "2": "Can be enabled via the Experimental Web Platform Features flag."}, "usage_perc_y": 32.35, "usage_perc_a": 0, "ucprefix": false, "parent": "stream", "keywords": "camera,device,photos,media stream,mediastream,Image Capture API", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": false}