{"title": "CSS Device Adaptation", "description": "A standard way to override the size of viewport in web page using the `@viewport` rule, standardizing and replacing Apple's own popular `<meta>` viewport implementation.", "spec": "https://www.w3.org/TR/css-device-adapt/", "status": "wd", "links": [{"url": "https://dev.opera.com/articles/view/an-introduction-to-meta-viewport-and-viewport/", "title": "Introduction to meta viewport and @viewport in Opera Mobile"}, {"url": "http://msdn.microsoft.com/en-us/library/ie/hh708740(v=vs.85).aspx", "title": "Device adaptation in Internet Explorer 10"}, {"url": "https://wpdev.uservoice.com/forums/257854-microsoft-edge-developer/suggestions/6777420-unprefix-and-support-all-viewport-properties", "title": "Microsoft Edge feature request on UserVoice"}, {"url": "https://code.google.com/p/chromium/issues/detail?id=155477", "title": "Chrome tracking bug"}, {"url": "https://bugs.webkit.org/show_bug.cgi?id=95959", "title": "WebKit tracking bug"}, {"url": "https://bugzilla.mozilla.org/show_bug.cgi?id=747754", "title": "Mozilla tracking bug"}], "bugs": [], "categories": ["CSS"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "a x #1", "11": "a x #1"}, "edge": {"12": "a x #1", "13": "a x #1", "14": "a x #1", "15": "a x #1", "16": "a x #1", "17": "a x #1", "18": "a x #1"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n", "46": "n", "47": "n", "48": "n", "49": "n", "50": "n", "51": "n", "52": "n", "53": "n", "54": "n", "55": "n", "56": "n", "57": "n", "58": "n", "59": "n", "60": "n", "61": "n"}, "chrome": {"4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n d", "30": "n d", "31": "n d", "32": "n d", "33": "n d", "34": "n d", "35": "n d", "36": "n d", "37": "n d", "38": "n d", "39": "n d", "40": "n d", "41": "n d", "42": "n d", "43": "n d", "44": "n d", "45": "n d", "46": "n d", "47": "n d", "48": "n d", "49": "n d", "50": "n d", "51": "n d", "52": "n d", "53": "n d", "54": "n d", "55": "n d", "56": "n d", "57": "n d", "58": "n d", "59": "n d", "60": "n d", "61": "n d", "62": "n d", "63": "n d", "64": "n d", "65": "n d", "66": "n d", "67": "n d", "68": "n d", "69": "n d"}, "safari": {"3.1": "n", "3.2": "n", "4": "n", "5": "n", "5.1": "n", "6": "n", "6.1": "n", "7": "n", "7.1": "n", "8": "n", "9": "n", "9.1": "n", "10": "n", "10.1": "n", "11": "n", "11.1": "n", "TP": "n"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n d", "41": "n d", "42": "n d", "43": "n d", "44": "n d", "45": "n d", "46": "n d", "47": "n d", "48": "n d", "49": "n d", "50": "n d", "51": "n d", "52": "n d"}, "ios_saf": {"3.2": "n", "4.0-4.1": "n", "4.2-4.3": "n", "5.0-5.1": "n", "6.0-6.1": "n", "7.0-7.1": "n", "8": "n", "8.1-8.4": "n", "9.0-9.2": "n", "9.3": "n", "10.0-10.2": "n", "10.3": "n", "11.0-11.2": "n", "11.3": "n"}, "op_mini": {"all": "a x #2"}, "android": {"2.1": "n", "2.2": "n", "2.3": "n", "3": "n", "4": "n", "4.1": "n", "4.2-4.3": "n", "4.4": "n", "4.4.3-4.4.4": "n", "62": "n"}, "bb": {"7": "n", "10": "n"}, "op_mob": {"10": "n", "11": "a x #2", "11.1": "a x #2", "11.5": "a x #2", "12": "a x #2", "12.1": "a x #2", "37": "n"}, "and_chr": {"66": "n"}, "and_ff": {"57": "n"}, "ie_mob": {"10": "a x #1", "11": "a x #1"}, "and_uc": {"11.8": "n"}, "samsung": {"4": "n", "5": "n", "6.2": "n"}, "and_qq": {"1.2": "n d"}, "baidu": {"7.12": "n"}}, "notes": "", "notes_by_num": {"1": "IE only supports the 'width' and 'height' properties.", "2": "Opera Mobile and Opera Mini only support the 'orientation' property."}, "usage_perc_y": 0, "usage_perc_a": 7.63, "ucprefix": false, "parent": "", "keywords": "viewport", "ie_id": "cssdeviceadaptation", "chrome_id": "4737164243894272", "firefox_id": "", "webkit_id": "", "shown": true}