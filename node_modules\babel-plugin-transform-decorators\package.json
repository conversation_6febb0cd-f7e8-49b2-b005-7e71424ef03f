{"name": "babel-plugin-transform-decorators", "version": "6.24.1", "description": "Compile class and object decorators to ES5", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-decorators", "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"babel-types": "^6.24.1", "babel-plugin-syntax-decorators": "^6.13.0", "babel-helper-explode-class": "^6.24.1", "babel-template": "^6.24.1", "babel-runtime": "^6.22.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.24.1"}}