"use strict";

module.exports = {
  builtins: {
    Symbol: "symbol",
    Promise: "promise",
    Map: "map",
    WeakMap: "weak-map",
    Set: "set",
    WeakSet: "weak-set",
    Observable: "observable",
    setImmediate: "set-immediate",
    clearImmediate: "clear-immediate",
    asap: "asap"
  },

  methods: {
    Array: {
      concat: "array/concat",
      copyWithin: "array/copy-within",
      entries: "array/entries",
      every: "array/every",
      fill: "array/fill",
      filter: "array/filter",
      findIndex: "array/find-index",
      find: "array/find",
      forEach: "array/for-each",
      from: "array/from",
      includes: "array/includes",
      indexOf: "array/index-of",

      join: "array/join",
      keys: "array/keys",
      lastIndexOf: "array/last-index-of",
      map: "array/map",
      of: "array/of",
      pop: "array/pop",
      push: "array/push",
      reduceRight: "array/reduce-right",
      reduce: "array/reduce",
      reverse: "array/reverse",
      shift: "array/shift",
      slice: "array/slice",
      some: "array/some",
      sort: "array/sort",
      splice: "array/splice",
      unshift: "array/unshift",
      values: "array/values"
    },

    JSON: {
      stringify: "json/stringify"
    },

    Object: {
      assign: "object/assign",
      create: "object/create",
      defineProperties: "object/define-properties",
      defineProperty: "object/define-property",
      entries: "object/entries",
      freeze: "object/freeze",
      getOwnPropertyDescriptor: "object/get-own-property-descriptor",
      getOwnPropertyDescriptors: "object/get-own-property-descriptors",
      getOwnPropertyNames: "object/get-own-property-names",
      getOwnPropertySymbols: "object/get-own-property-symbols",
      getPrototypeOf: "object/get-prototype-of",
      isExtensible: "object/is-extensible",
      isFrozen: "object/is-frozen",
      isSealed: "object/is-sealed",
      is: "object/is",
      keys: "object/keys",
      preventExtensions: "object/prevent-extensions",
      seal: "object/seal",
      setPrototypeOf: "object/set-prototype-of",
      values: "object/values"
    },

    RegExp: {
      escape: "regexp/escape" },

    Math: {
      acosh: "math/acosh",
      asinh: "math/asinh",
      atanh: "math/atanh",
      cbrt: "math/cbrt",
      clz32: "math/clz32",
      cosh: "math/cosh",
      expm1: "math/expm1",
      fround: "math/fround",
      hypot: "math/hypot",
      imul: "math/imul",
      log10: "math/log10",
      log1p: "math/log1p",
      log2: "math/log2",
      sign: "math/sign",
      sinh: "math/sinh",
      tanh: "math/tanh",
      trunc: "math/trunc",
      iaddh: "math/iaddh",
      isubh: "math/isubh",
      imulh: "math/imulh",
      umulh: "math/umulh"
    },

    Symbol: {
      for: "symbol/for",
      hasInstance: "symbol/has-instance",
      isConcatSpreadable: "symbol/is-concat-spreadable",
      iterator: "symbol/iterator",
      keyFor: "symbol/key-for",
      match: "symbol/match",
      replace: "symbol/replace",
      search: "symbol/search",
      species: "symbol/species",
      split: "symbol/split",
      toPrimitive: "symbol/to-primitive",
      toStringTag: "symbol/to-string-tag",
      unscopables: "symbol/unscopables"
    },

    String: {
      at: "string/at",
      codePointAt: "string/code-point-at",
      endsWith: "string/ends-with",
      fromCodePoint: "string/from-code-point",
      includes: "string/includes",
      matchAll: "string/match-all",
      padLeft: "string/pad-left",
      padRight: "string/pad-right",
      padStart: "string/pad-start",
      padEnd: "string/pad-end",
      raw: "string/raw",
      repeat: "string/repeat",
      startsWith: "string/starts-with",
      trim: "string/trim",
      trimLeft: "string/trim-left",
      trimRight: "string/trim-right",
      trimStart: "string/trim-start",
      trimEnd: "string/trim-end"
    },

    Number: {
      EPSILON: "number/epsilon",
      isFinite: "number/is-finite",
      isInteger: "number/is-integer",
      isNaN: "number/is-nan",
      isSafeInteger: "number/is-safe-integer",
      MAX_SAFE_INTEGER: "number/max-safe-integer",
      MIN_SAFE_INTEGER: "number/min-safe-integer",
      parseFloat: "number/parse-float",
      parseInt: "number/parse-int"
    },

    Reflect: {
      apply: "reflect/apply",
      construct: "reflect/construct",
      defineProperty: "reflect/define-property",
      deleteProperty: "reflect/delete-property",
      enumerate: "reflect/enumerate",
      getOwnPropertyDescriptor: "reflect/get-own-property-descriptor",
      getPrototypeOf: "reflect/get-prototype-of",
      get: "reflect/get",
      has: "reflect/has",
      isExtensible: "reflect/is-extensible",
      ownKeys: "reflect/own-keys",
      preventExtensions: "reflect/prevent-extensions",
      setPrototypeOf: "reflect/set-prototype-of",
      set: "reflect/set",
      defineMetadata: "reflect/define-metadata",
      deleteMetadata: "reflect/delete-metadata",
      getMetadata: "reflect/get-metadata",
      getMetadataKeys: "reflect/get-metadata-keys",
      getOwnMetadata: "reflect/get-own-metadata",
      getOwnMetadataKeys: "reflect/get-own-metadata-keys",
      hasMetadata: "reflect/has-metadata",
      hasOwnMetadata: "reflect/has-own-metadata",
      metadata: "reflect/metadata"
    },

    System: {
      global: "system/global"
    },

    Error: {
      isError: "error/is-error" },

    Date: {},

    Function: {}
  }
};