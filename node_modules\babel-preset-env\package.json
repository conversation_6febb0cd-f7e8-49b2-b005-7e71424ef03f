{"name": "babel-preset-env", "version": "1.6.1", "description": "A Babel preset for each environment.", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "repository": "https://github.com/babel/babel-preset-env", "main": "lib/index.js", "scripts": {"build": "rimraf lib && babel src -d lib", "build-data": "node ./scripts/build-data.js", "changelog": "git log `git describe --tags --abbrev=0`..HEAD --pretty=format:' * %s (%an)' | grep -v 'Merge pull request'", "coverage": "BABEL_ENV=test nyc npm run test", "coverage-ci": "nyc report --reporter=json && codecov -f coverage/coverage-final.json", "dev": "babel -w src -d lib", "fix": "eslint . --fix", "lint": "eslint .", "prepublish": "npm run build", "test": "npm run build && npm run test-only", "test-ci": "nyc npm run test", "test-only": "mocha ./test --compilers js:babel-register -t 10000"}, "dependencies": {"babel-plugin-check-es2015-constants": "^6.22.0", "babel-plugin-syntax-trailing-function-commas": "^6.22.0", "babel-plugin-transform-async-to-generator": "^6.22.0", "babel-plugin-transform-es2015-arrow-functions": "^6.22.0", "babel-plugin-transform-es2015-block-scoped-functions": "^6.22.0", "babel-plugin-transform-es2015-block-scoping": "^6.23.0", "babel-plugin-transform-es2015-classes": "^6.23.0", "babel-plugin-transform-es2015-computed-properties": "^6.22.0", "babel-plugin-transform-es2015-destructuring": "^6.23.0", "babel-plugin-transform-es2015-duplicate-keys": "^6.22.0", "babel-plugin-transform-es2015-for-of": "^6.23.0", "babel-plugin-transform-es2015-function-name": "^6.22.0", "babel-plugin-transform-es2015-literals": "^6.22.0", "babel-plugin-transform-es2015-modules-amd": "^6.22.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.23.0", "babel-plugin-transform-es2015-modules-systemjs": "^6.23.0", "babel-plugin-transform-es2015-modules-umd": "^6.23.0", "babel-plugin-transform-es2015-object-super": "^6.22.0", "babel-plugin-transform-es2015-parameters": "^6.23.0", "babel-plugin-transform-es2015-shorthand-properties": "^6.22.0", "babel-plugin-transform-es2015-spread": "^6.22.0", "babel-plugin-transform-es2015-sticky-regex": "^6.22.0", "babel-plugin-transform-es2015-template-literals": "^6.22.0", "babel-plugin-transform-es2015-typeof-symbol": "^6.23.0", "babel-plugin-transform-es2015-unicode-regex": "^6.22.0", "babel-plugin-transform-exponentiation-operator": "^6.22.0", "babel-plugin-transform-regenerator": "^6.22.0", "browserslist": "^2.1.2", "invariant": "^2.2.2", "semver": "^5.3.0"}, "devDependencies": {"babel-cli": "^6.23.0", "babel-eslint": "^7.1.1", "babel-helper-fixtures": "^6.22.0", "babel-helper-plugin-test-runner": "^6.22.0", "babel-plugin-istanbul": "^3.1.2", "babel-preset-env": "^1.4.0", "babel-register": "^6.23.0", "chai": "^3.5.0", "codecov": "^1.0.1", "compat-table": "kangax/compat-table#957f1ff15972e8fb2892a172f985e9af27bf1c75", "eslint": "^3.17.1", "eslint-config-babel": "^6.0.0", "eslint-plugin-flowtype": "^2.29.1", "fs-extra": "~2.0.0", "lodash": "^4.17.4", "mocha": "^3.2.0", "nyc": "^10.1.2", "electron-to-chromium": "^1.3.11", "rimraf": "^2.6.1"}, "babel": {"presets": [["env", {"loose": true}]], "env": {"test": {"plugins": ["istanbul"]}}}, "nyc": {"all": true, "include": ["src/*.js"], "instrument": false, "sourceMap": false}}