{"title": "Datalist element", "description": "Method of setting a list of options for a user to select in a text field, while leaving the ability to enter a custom value.", "spec": "https://html.spec.whatwg.org/multipage/forms.html#the-datalist-element", "status": "ls", "links": [{"url": "https://hacks.mozilla.org/2010/11/firefox-4-html5-forms/", "title": "Mozilla Hacks article"}, {"url": "http://afarkas.github.com/webshim/demos/", "title": "HTML5 Library including datalist support"}, {"url": "https://developer.mozilla.org/en/HTML/Element/datalist", "title": "MDN Web Docs - datalist"}, {"url": "https://www.webplatform.org/docs/html/elements/datalist", "title": "WebPlatform Docs"}, {"url": "http://demo.agektmr.com/datalist/", "title": "<PERSON><PERSON>'s options demos & tests"}, {"url": "https://github.com/thgreasi/datalist-polyfill", "title": "Minimal Datalist polyfill w/tutorial"}, {"url": "https://github.com/mfranzke/datalist-polypill", "title": "Minimal and library dependency-free vanilla JavaScript polypill"}, {"url": "https://github.com/mfranzke/datalist-polyfill", "title": "Minimal and library dependency-free vanilla JavaScript polyfill"}], "bugs": [], "categories": ["HTML5"], "stats": {"ie": {"5.5": "n", "6": "p", "7": "p", "8": "p", "9": "p", "10": "a #2", "11": "a #2"}, "edge": {"12": "a #2", "13": "a #2", "14": "a #2", "15": "a #2", "16": "a #2", "17": "a #2", "18": "a #2"}, "firefox": {"2": "p", "3": "p", "3.5": "p", "3.6": "p", "4": "a #3", "5": "a #3", "6": "a #3", "7": "a #3", "8": "a #3", "9": "a #3", "10": "a #3", "11": "a #3", "12": "a #3", "13": "a #3", "14": "a #3", "15": "a #3", "16": "a #3", "17": "a #3", "18": "a #3", "19": "a #3", "20": "a #3", "21": "a #3", "22": "a #3", "23": "a #3", "24": "a #3", "25": "a #3", "26": "a #3", "27": "a #3", "28": "a #3", "29": "a #3", "30": "a #3", "31": "a #3", "32": "a #3", "33": "a #3", "34": "a #3", "35": "a #3", "36": "a #3", "37": "a #3", "38": "a #3", "39": "a #3", "40": "a #3", "41": "a #3", "42": "a #3", "43": "a #3", "44": "a #3", "45": "a #3", "46": "a #3", "47": "a #3", "48": "a #3", "49": "a #3", "50": "a #3", "51": "a #3", "52": "a #3", "53": "a #3", "54": "a #3", "55": "a #3", "56": "a #3", "57": "a #3", "58": "a #3", "59": "a #3", "60": "a #3", "61": "a #3"}, "chrome": {"4": "p", "5": "p", "6": "p", "7": "p", "8": "p", "9": "p", "10": "p", "11": "p", "12": "p", "13": "p", "14": "p", "15": "p", "16": "p", "17": "p", "18": "p", "19": "p", "20": "a #1", "21": "a #1", "22": "a #1", "23": "a #1", "24": "a #1", "25": "a #1", "26": "a #1", "27": "a #1", "28": "a #1", "29": "a #1", "30": "a #1", "31": "a #1", "32": "a #1", "33": "a #1", "34": "a #1", "35": "a #1", "36": "a #1", "37": "a #1", "38": "a #1", "39": "a #1", "40": "a #1", "41": "a #1", "42": "a #1", "43": "a #1", "44": "a #1", "45": "a #1", "46": "a #1", "47": "a #1", "48": "a #1", "49": "a #1", "50": "a #1", "51": "a #1", "52": "a #1", "53": "a #1", "54": "a #1", "55": "a #1", "56": "a #1", "57": "a #1", "58": "a #1", "59": "a #1", "60": "a #1", "61": "a #1", "62": "a #1", "63": "a #1", "64": "a #1", "65": "a #1", "66": "a #1", "67": "a #1", "68": "a #1", "69": "a #1"}, "safari": {"3.1": "p", "3.2": "p", "4": "p", "5": "p", "5.1": "p", "6": "p", "6.1": "p", "7": "p", "7.1": "p", "8": "p", "9": "p", "9.1": "p", "10": "p", "10.1": "p", "11": "p", "11.1": "p", "TP": "p"}, "opera": {"9": "y", "9.5-9.6": "y", "10.0-10.1": "y", "10.5": "y", "10.6": "y", "11": "y", "11.1": "y", "11.5": "y", "11.6": "y", "12": "y", "12.1": "y", "15": "a #1", "16": "a #1", "17": "a #1", "18": "a #1", "19": "a #1", "20": "a #1", "21": "a #1", "22": "a #1", "23": "a #1", "24": "a #1", "25": "a #1", "26": "a #1", "27": "a #1", "28": "a #1", "29": "a #1", "30": "a #1", "31": "a #1", "32": "a #1", "33": "a #1", "34": "a #1", "35": "a #1", "36": "a #1", "37": "a #1", "38": "a #1", "39": "a #1", "40": "a #1", "41": "a #1", "42": "a #1", "43": "a #1", "44": "a #1", "45": "a #1", "46": "a #1", "47": "a #1", "48": "a #1", "49": "a #1", "50": "a #1", "51": "a #1", "52": "a #1"}, "ios_saf": {"3.2": "p", "4.0-4.1": "p", "4.2-4.3": "p", "5.0-5.1": "p", "6.0-6.1": "p", "7.0-7.1": "p", "8": "p", "8.1-8.4": "p", "9.0-9.2": "p", "9.3": "p", "10.0-10.2": "p", "10.3": "p", "11.0-11.2": "p", "11.3": "p"}, "op_mini": {"all": "n"}, "android": {"2.1": "p", "2.2": "p", "2.3": "p", "3": "p", "4": "p", "4.1": "p", "4.2-4.3": "p", "4.4": "p", "4.4.3-4.4.4": "y", "62": "a #1"}, "bb": {"7": "p", "10": "y"}, "op_mob": {"10": "y", "11": "y", "11.1": "y", "11.5": "y", "12": "y", "12.1": "y", "37": "p"}, "and_chr": {"66": "y"}, "and_ff": {"57": "a #3"}, "ie_mob": {"10": "p", "11": "p"}, "and_uc": {"11.8": "p"}, "samsung": {"4": "y", "5": "y", "6.2": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "While most commonly used on text fields, datalists can also be used on other input types. IE11 supports the element on `range` fields. Chrome and Opera also support datalists to suggest given values on `range`, `color` and date/time fields. ", "notes_by_num": {"1": "Partial support refers to [a bug](https://bugs.chromium.org/p/chromium/issues/detail?id=773041) where long lists of items are unscrollable resulting in unselectable options.", "2": "Partial support in IE refers to [significantly buggy behavior](http://playground.onereason.eu/2013/04/ie10s-lousy-support-for-datalists/) (IE11+ does send the input and change events upon selection). ", "3": "Partial support refers to no support for datalists on non-text fields (e.g. number, [range](https://bugzilla.mozilla.org/show_bug.cgi?id=841942), [color](https://bugzilla.mozilla.org/show_bug.cgi?id=960984))."}, "usage_perc_y": 33.82, "usage_perc_a": 39.56, "ucprefix": false, "parent": "forms", "keywords": "list attribute", "ie_id": "datalistelement", "chrome_id": "6090950820495360", "firefox_id": "", "webkit_id": "", "shown": true}