{"title": ":indeterminate CSS pseudo-class", "description": "The `:indeterminate` pseudo-class matches indeterminate checkboxes, indeterminate `<progress>` bars, and radio buttons with no checked button in their radio button group.", "spec": "https://drafts.csswg.org/selectors-4/#indeterminate", "status": "unoff", "links": [{"url": "https://html.spec.whatwg.org/multipage/scripting.html#selector-indeterminate", "title": "HTML specification for `:indeterminate`"}, {"url": "https://developer.mozilla.org/en-US/docs/Web/CSS/:indeterminate", "title": "MDN Web Docs - CSS :indeterminate"}, {"url": "https://developer.microsoft.com/en-us/microsoft-edge/platform/issues/7124038/", "title": "EdgeHTML issue 7124038 - `:indeterminate` pseudo-class doesn't match radio buttons"}, {"url": "https://bugzilla.mozilla.org/show_bug.cgi?id=885359", "title": "Mozilla Bug 885359 - Radio groups without a selected radio button should have `:indeterminate` applying"}, {"url": "https://bugs.webkit.org/show_bug.cgi?id=156270", "title": "WebKit Bug 156270 - `:indeterminate` pseudo-class should match radios whose group has no checked radio"}, {"url": "http://jsbin.com/zumoqu/edit?html,css,js,output", "title": "JS Bin testcase"}], "bugs": [], "categories": ["CSS"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "a #1 #2", "10": "a #1", "11": "a #1"}, "edge": {"12": "a #1", "13": "a #1", "14": "a #1", "15": "a #1", "16": "a #1", "17": "a #1", "18": "a #1"}, "firefox": {"2": "u", "3": "u", "3.5": "u", "3.6": "u", "4": "a #1 #2", "5": "a #1 #2", "6": "a #1", "7": "a #1", "8": "a #1", "9": "a #1", "10": "a #1", "11": "a #1", "12": "a #1", "13": "a #1", "14": "a #1", "15": "a #1", "16": "a #1", "17": "a #1", "18": "a #1", "19": "a #1", "20": "a #1", "21": "a #1", "22": "a #1", "23": "a #1", "24": "a #1", "25": "a #1", "26": "a #1", "27": "a #1", "28": "a #1", "29": "a #1", "30": "a #1", "31": "a #1", "32": "a #1", "33": "a #1", "34": "a #1", "35": "a #1", "36": "a #1", "37": "a #1", "38": "a #1", "39": "a #1", "40": "a #1", "41": "a #1", "42": "a #1", "43": "a #1", "44": "a #1", "45": "a #1", "46": "a #1", "47": "a #1", "48": "a #1", "49": "a #1", "50": "a #1", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y"}, "chrome": {"4": "u", "5": "u", "6": "u", "7": "u", "8": "u", "9": "u", "10": "u", "11": "u", "12": "u", "13": "u", "14": "u", "15": "a #1", "16": "a #1", "17": "a #1", "18": "a #1", "19": "a #1", "20": "a #1", "21": "a #1", "22": "a #1", "23": "a #1", "24": "a #1", "25": "a #1", "26": "a #1", "27": "a #1", "28": "a #1", "29": "a #1", "30": "a #1", "31": "a #1", "32": "a #1", "33": "a #1", "34": "a #1", "35": "a #1", "36": "a #1", "37": "a #1", "38": "a #1", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y", "62": "y", "63": "y", "64": "y", "65": "y", "66": "y", "67": "y", "68": "y", "69": "y"}, "safari": {"3.1": "u", "3.2": "u", "4": "u", "5": "u", "5.1": "a #1 #2", "6": "u", "6.1": "a #1", "7": "a #1", "7.1": "a #1", "8": "a #1", "9": "a #1", "9.1": "a #1", "10": "a #1", "10.1": "y", "11": "y", "11.1": "y", "TP": "y"}, "opera": {"9": "u", "9.5-9.6": "u", "10.0-10.1": "u", "10.5": "u", "10.6": "u", "11": "u", "11.1": "u", "11.5": "u", "11.6": "a #3", "12": "a #3", "12.1": "a #3", "15": "a #1", "16": "a #1", "17": "a #1", "18": "a #1", "19": "a #1", "20": "a #1", "21": "a #1", "22": "a #1", "23": "a #1", "24": "a #1", "25": "a #1", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y"}, "ios_saf": {"3.2": "u", "4.0-4.1": "u", "4.2-4.3": "u", "5.0-5.1": "u", "6.0-6.1": "u", "7.0-7.1": "a #1", "8": "a #1", "8.1-8.4": "a #1", "9.0-9.2": "a #1", "9.3": "a #1", "10.0-10.2": "a #1", "10.3": "y", "11.0-11.2": "y", "11.3": "y"}, "op_mini": {"all": "a #3"}, "android": {"2.1": "u", "2.2": "u", "2.3": "u", "3": "u", "4": "a #1 #2", "4.1": "a #1 #2", "4.2-4.3": "a #1 #2", "4.4": "a #1", "4.4.3-4.4.4": "u", "62": "y"}, "bb": {"7": "u", "10": "a #1"}, "op_mob": {"10": "u", "11": "u", "11.1": "u", "11.5": "u", "12": "u", "12.1": "a #3", "37": "y"}, "and_chr": {"66": "y"}, "and_ff": {"57": "a #1"}, "ie_mob": {"10": "a #1", "11": "a #1"}, "and_uc": {"11.8": "y"}, "samsung": {"4": "y", "5": "y", "6.2": "y"}, "and_qq": {"1.2": "y"}, "baidu": {"7.12": "y"}}, "notes": "", "notes_by_num": {"1": "Doesn't match radio buttons whose radio button group lacks a checked radio button", "2": "Doesn't support the `<progress>` element", "3": "Doesn't match indeterminate `<progress>` bars"}, "usage_perc_y": 85.47, "usage_perc_a": 11.9, "ucprefix": false, "parent": "", "keywords": ":indeterminate,indeterminate", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}