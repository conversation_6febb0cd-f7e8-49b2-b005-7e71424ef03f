{"name": "babel-plugin-transform-async-generator-functions", "version": "6.24.1", "description": "Turn async generator functions into ES2015 generators", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-transform-async-generator-functions", "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"babel-helper-remap-async-to-generator": "^6.24.1", "babel-plugin-syntax-async-generators": "^6.5.0", "babel-runtime": "^6.22.0"}, "devDependencies": {"babel-helper-plugin-test-runner": "^6.24.1"}}