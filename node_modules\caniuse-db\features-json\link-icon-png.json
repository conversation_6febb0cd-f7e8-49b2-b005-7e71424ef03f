{"title": "PNG favicons", "description": "Icon used by browsers to identify a webpage or site. While all browsers support the `.ico` format, the PNG format can be preferable.", "spec": "https://html.spec.whatwg.org/multipage/semantics.html#rel-icon", "status": "ls", "links": [{"url": "https://css-tricks.com/favicon-quiz/", "title": "Detailed info on favicons for various uses"}], "bugs": [{"description": "IE 11 [lost the support](https://connect.microsoft.com/IE/feedbackdetail/view/800076/internet-explorer-11-lost-support-for-non-rgb-a-png-format-images-inside-favicon-ico) for non-RGB/a PNG format images inside FavIcon.ico, does work in IE 10."}], "categories": ["HTML5"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "y"}, "edge": {"12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y"}, "firefox": {"2": "y", "3": "y", "3.5": "y", "3.6": "y", "4": "y", "5": "y", "6": "y", "7": "y", "8": "y", "9": "y", "10": "y", "11": "y", "12": "y", "13": "y", "14": "y", "15": "y", "16": "y", "17": "y", "18": "y", "19": "y", "20": "y", "21": "y", "22": "y", "23": "y", "24": "y", "25": "y", "26": "y", "27": "y", "28": "y", "29": "y", "30": "y", "31": "y", "32": "y", "33": "y", "34": "y", "35": "y", "36": "y", "37": "y", "38": "y", "39": "y", "40": "y", "41": "y", "42": "y", "43": "y", "44": "y", "45": "y", "46": "y", "47": "y", "48": "y", "49": "y", "50": "y", "51": "y", "52": "y", "53": "y", "54": "y", "55": "y", "56": "y", "57": "y", "58": "y", "59": "y", "60": "y", "61": "y"}, "chrome": {"4": "y #1", "5": "y #1", "6": "y #1", "7": "y #1", "8": "y #1", "9": "y #1", "10": "y #1", "11": "y #1", "12": "y #1", "13": "y #1", "14": "y #1", "15": "y #1", "16": "y #1", "17": "y #1", "18": "y #1", "19": "y #1", "20": "y #1", "21": "y #1", "22": "y #1", "23": "y #1", "24": "y #1", "25": "y #1", "26": "y #1", "27": "y #1", "28": "y #1", "29": "y #1", "30": "y #1", "31": "y #1", "32": "y #1", "33": "y #1", "34": "y #1", "35": "y #1", "36": "y #1", "37": "y #1", "38": "y #1", "39": "y #1", "40": "y #1", "41": "y #1", "42": "y #1", "43": "y #1", "44": "y #1", "45": "y #1", "46": "y #1", "47": "y #1", "48": "y #1", "49": "y #1", "50": "y #1", "51": "y #1", "52": "y #1", "53": "y #1", "54": "y #1", "55": "y #1", "56": "y #1", "57": "y #1", "58": "y #1", "59": "y #1", "60": "y #1", "61": "y #1", "62": "y #1", "63": "y #1", "64": "y #1", "65": "y #1", "66": "y #1", "67": "y #1", "68": "y #1", "69": "y #1"}, "safari": {"3.1": "y #2", "3.2": "y #2", "4": "y #2", "5": "y #2", "5.1": "y #2", "6": "y #2", "6.1": "y #2", "7": "y #2", "7.1": "y #2", "8": "y #2", "9": "y #2", "9.1": "y #2", "10": "y #2", "10.1": "y #2", "11": "y #2", "11.1": "y #2", "TP": "y #2"}, "opera": {"9": "y #3", "9.5-9.6": "y #3", "10.0-10.1": "y #3", "10.5": "y #3", "10.6": "y #3", "11": "y #3", "11.1": "y #3", "11.5": "y #3", "11.6": "y #3", "12": "y #3", "12.1": "y #3", "15": "y #1", "16": "y #1", "17": "y #1", "18": "y #1", "19": "y #1", "20": "y #1", "21": "y #1", "22": "y #1", "23": "y #1", "24": "y #1", "25": "y #1", "26": "y #1", "27": "y #1", "28": "y #1", "29": "y #1", "30": "y #1", "31": "y #1", "32": "y #1", "33": "y #1", "34": "y #1", "35": "y #1", "36": "y #1", "37": "y #1", "38": "y #1", "39": "y #1", "40": "y #1", "41": "y #1", "42": "y #1", "43": "y #1", "44": "y #1", "45": "y #1", "46": "y #1", "47": "y #1", "48": "y #1", "49": "y #1", "50": "y #1", "51": "y #1", "52": "y #1"}, "ios_saf": {"3.2": "n #4", "4.0-4.1": "n #4", "4.2-4.3": "n #4", "5.0-5.1": "n #4", "6.0-6.1": "n #4", "7.0-7.1": "n #4", "8": "n #4", "8.1-8.4": "n #4", "9.0-9.2": "n #4", "9.3": "n #4", "10.0-10.2": "n #4", "10.3": "n #4", "11.0-11.2": "n #4", "11.3": "n #4"}, "op_mini": {"all": "n #4"}, "android": {"2.1": "y", "2.2": "y", "2.3": "y", "3": "y", "4": "y", "4.1": "y", "4.2-4.3": "y", "4.4": "y #3", "4.4.3-4.4.4": "y #3", "62": "y #3"}, "bb": {"7": "y", "10": "n #4"}, "op_mob": {"10": "n #4", "11": "n #4", "11.1": "n #4", "11.5": "n #4", "12": "n #4", "12.1": "n #4", "37": "n #4"}, "and_chr": {"66": "y"}, "and_ff": {"57": "y"}, "ie_mob": {"10": "n #4", "11": "n #4"}, "and_uc": {"11.8": "y #2"}, "samsung": {"4": "y #3", "5": "y", "6.2": "y"}, "and_qq": {"1.2": "y #1"}, "baidu": {"7.12": "y"}}, "notes": "Win8/IE10+ and iOS Safari support other types of icons for webpages too, using alternate tags.\r\n\r\nSee also [SVG favicons](#feat=link-icon-svg).", "notes_by_num": {"1": "If both ICO and PNG are available, will use ICO over PNG if ICO has better matching sizes set.", "2": "If both ICO and PNG are available, will ALWAYS use ICO file, regardless of sizes set.", "3": "If multiple formats are available, will use the last one loaded, regardless of sizes (effectively picks at random).", "4": "Does not use favicons at all (but may have alternative for bookmarks, etc.)."}, "usage_perc_y": 84.07, "usage_perc_a": 0, "ucprefix": false, "parent": "", "keywords": "", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}