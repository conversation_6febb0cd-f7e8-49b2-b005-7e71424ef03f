{"title": "CSS Appearance", "description": "The `appearance` property defines how elements (particularly form controls) appear by default. By setting the value to `none` the default appearance can be entirely redefined using other CSS properties.", "spec": "https://drafts.csswg.org/css-ui-4/#appearance-switching", "status": "wd", "links": [{"url": "https://css-tricks.com/almanac/properties/a/appearance/", "title": "CSS Tricks article"}], "bugs": [], "categories": ["CSS"], "stats": {"ie": {"5.5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n"}, "edge": {"12": "a #1 #2", "13": "a #1 #2", "14": "a #1 #2", "15": "a #1 #2", "16": "a #1 #2", "17": "a #1 #2", "18": "a #1 #2"}, "firefox": {"2": "a x #1 #3", "3": "a x #1 #3", "3.5": "a x #1 #3", "3.6": "a x #1 #3", "4": "a x #1 #3", "5": "a x #1 #3", "6": "a x #1 #3", "7": "a x #1 #3", "8": "a x #1 #3", "9": "a x #1 #3", "10": "a x #1 #3", "11": "a x #1 #3", "12": "a x #1 #3", "13": "a x #1 #3", "14": "a x #1 #3", "15": "a x #1 #3", "16": "a x #1 #3", "17": "a x #1 #3", "18": "a x #1 #3", "19": "a x #1 #3", "20": "a x #1 #3", "21": "a x #1 #3", "22": "a x #1 #3", "23": "a x #1 #3", "24": "a x #1 #3", "25": "a x #1 #3", "26": "a x #1 #3", "27": "a x #1 #3", "28": "a x #1 #3", "29": "a x #1 #3", "30": "a x #1 #3", "31": "a x #1 #3", "32": "a x #1 #3", "33": "a x #1 #3", "34": "a x #1 #3", "35": "a x #1", "36": "a x #1", "37": "a x #1", "38": "a x #1", "39": "a x #1", "40": "a x #1", "41": "a x #1", "42": "a x #1", "43": "a x #1", "44": "a x #1", "45": "a x #1", "46": "a x #1", "47": "a x #1", "48": "a x #1", "49": "a x #1", "50": "a x #1", "51": "a x #1", "52": "a x #1", "53": "a x #1", "54": "a x #1", "55": "a x #1", "56": "a x #1", "57": "a x #1", "58": "a x #1", "59": "a x #1", "60": "a x #1", "61": "a x #1"}, "chrome": {"4": "a x #1", "5": "a x #1", "6": "a x #1", "7": "a x #1", "8": "a x #1", "9": "a x #1", "10": "a x #1", "11": "a x #1", "12": "a x #1", "13": "a x #1", "14": "a x #1", "15": "a x #1", "16": "a x #1", "17": "a x #1", "18": "a x #1", "19": "a x #1", "20": "a x #1", "21": "a x #1", "22": "a x #1", "23": "a x #1", "24": "a x #1", "25": "a x #1", "26": "a x #1", "27": "a x #1", "28": "a x #1", "29": "a x #1", "30": "a x #1", "31": "a x #1", "32": "a x #1", "33": "a x #1", "34": "a x #1", "35": "a x #1", "36": "a x #1", "37": "a x #1", "38": "a x #1", "39": "a x #1", "40": "a x #1", "41": "a x #1", "42": "a x #1", "43": "a x #1", "44": "a x #1", "45": "a x #1", "46": "a x #1", "47": "a x #1", "48": "a x #1", "49": "a x #1", "50": "a x #1", "51": "a x #1", "52": "a x #1", "53": "a x #1", "54": "a x #1", "55": "a x #1", "56": "a x #1", "57": "a x #1", "58": "a x #1", "59": "a x #1", "60": "a x #1", "61": "a x #1", "62": "a x #1", "63": "a x #1", "64": "a x #1", "65": "a x #1", "66": "a x #1", "67": "a x #1", "68": "a x #1", "69": "a x #1"}, "safari": {"3.1": "a x #1", "3.2": "a x #1", "4": "a x #1", "5": "a x #1", "5.1": "a x #1", "6": "a x #1", "6.1": "a x #1", "7": "a x #1", "7.1": "a x #1", "8": "a x #1", "9": "a x #1", "9.1": "a x #1", "10": "a x #1", "10.1": "a x #1", "11": "a x #1", "11.1": "a x #1", "TP": "a x #1"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "a x #1", "16": "a x #1", "17": "a x #1", "18": "a x #1", "19": "a x #1", "20": "a x #1", "21": "a x #1", "22": "a x #1", "23": "a x #1", "24": "a x #1", "25": "a x #1", "26": "a x #1", "27": "a x #1", "28": "a x #1", "29": "a x #1", "30": "a x #1", "31": "a x #1", "32": "a x #1", "33": "a x #1", "34": "a x #1", "35": "a x #1", "36": "a x #1", "37": "a x #1", "38": "a x #1", "39": "a x #1", "40": "a x #1", "41": "a x #1", "42": "a x #1", "43": "a x #1", "44": "a x #1", "45": "a x #1", "46": "a x #1", "47": "a x #1", "48": "a x #1", "49": "a x #1", "50": "a x #1", "51": "a x #1", "52": "a x #1"}, "ios_saf": {"3.2": "a x #1", "4.0-4.1": "a x #1", "4.2-4.3": "a x #1", "5.0-5.1": "a x #1", "6.0-6.1": "a x #1", "7.0-7.1": "a x #1", "8": "a x #1", "8.1-8.4": "a x #1", "9.0-9.2": "a x #1", "9.3": "a x #1", "10.0-10.2": "a x #1", "10.3": "a x #1", "11.0-11.2": "a x #1", "11.3": "a x #1"}, "op_mini": {"all": "n"}, "android": {"2.1": "a x #1", "2.2": "a x #1", "2.3": "a x #1", "3": "a x #1", "4": "a x #1", "4.1": "a x #1", "4.2-4.3": "a x #1", "4.4": "a x #1", "4.4.3-4.4.4": "a x #1", "62": "a x #1"}, "bb": {"7": "a x #1", "10": "a x #1"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "a x #1"}, "and_chr": {"66": "a x #1"}, "and_ff": {"57": "a x #1"}, "ie_mob": {"10": "n", "11": "a #1 #2"}, "and_uc": {"11.8": "a x #1"}, "samsung": {"4": "a x #1", "5": "a x #1", "6.2": "a x #1"}, "and_qq": {"1.2": "a x #1"}, "baidu": {"7.12": "a x #1"}}, "notes": "", "notes_by_num": {"1": "The appearance property is supported with the `none` value, but not `auto`. WebKit, Blink, and Gecko browsers also support additional vendor specific values.", "2": "Microsoft Edge and IE Mobile support this property with the `-webkit-` prefix, rather than `-ms-` for interop reasons.", "3": "-moz-appearance:none doesn't remove the dropdown arrow in select tag"}, "usage_perc_y": 0, "usage_perc_a": 92.14, "ucprefix": false, "parent": "", "keywords": "", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}