{"title": "CSS scrollbar styling", "description": "Non-standard methods of styling scrollbars.", "spec": "https://webkit.org/blog/363/styling-scrollbars/", "status": "unoff", "links": [{"url": "https://bugzilla.mozilla.org/show_bug.cgi?id=77790", "title": "Firefox support bug"}, {"url": "https://stackoverflow.com/questions/9251354/css-customized-scroll-bar-in-div/14150577#14150577", "title": "Stackoverflow article discussiong cross-browser support"}, {"url": "http://codemug.com/html/custom-scrollbars-using-css/", "title": "Tutorial for IE & WebKit/Blink browsers"}, {"url": "http://utatti.github.io/perfect-scrollbar/", "title": "\"perfect-scrollbar\" - Minimal custom scrollbar plugin"}, {"url": "http://manos.malihu.gr/jquery-custom-content-scroller/", "title": "jQuery custom content scroller"}], "bugs": [], "categories": ["CSS"], "stats": {"ie": {"5.5": "a #1", "6": "a #1", "7": "a #1", "8": "a #1", "9": "a #1", "10": "a #1", "11": "a #1"}, "edge": {"12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n"}, "firefox": {"2": "n", "3": "n", "3.5": "n", "3.6": "n", "4": "n", "5": "n", "6": "n", "7": "n", "8": "n", "9": "n", "10": "n", "11": "n", "12": "n", "13": "n", "14": "n", "15": "n", "16": "n", "17": "n", "18": "n", "19": "n", "20": "n", "21": "n", "22": "n", "23": "n", "24": "n", "25": "n", "26": "n", "27": "n", "28": "n", "29": "n", "30": "n", "31": "n", "32": "n", "33": "n", "34": "n", "35": "n", "36": "n", "37": "n", "38": "n", "39": "n", "40": "n", "41": "n", "42": "n", "43": "n", "44": "n", "45": "n", "46": "n", "47": "n", "48": "n", "49": "n", "50": "n", "51": "n", "52": "n", "53": "n", "54": "n", "55": "n", "56": "n", "57": "n", "58": "n", "59": "n", "60": "n", "61": "n"}, "chrome": {"4": "y x #2", "5": "y x #2", "6": "y x #2", "7": "y x #2", "8": "y x #2", "9": "y x #2", "10": "y x #2", "11": "y x #2", "12": "y x #2", "13": "y x #2", "14": "y x #2", "15": "y x #2", "16": "y x #2", "17": "y x #2", "18": "y x #2", "19": "y x #2", "20": "y x #2", "21": "y x #2", "22": "y x #2", "23": "y x #2", "24": "y x #2", "25": "y x #2", "26": "y x #2", "27": "y x #2", "28": "y x #2", "29": "y x #2", "30": "y x #2", "31": "y x #2", "32": "y x #2", "33": "y x #2", "34": "y x #2", "35": "y x #2", "36": "y x #2", "37": "y x #2", "38": "y x #2", "39": "y x #2", "40": "y x #2", "41": "y x #2", "42": "y x #2", "43": "y x #2", "44": "y x #2", "45": "y x #2", "46": "y x #2", "47": "y x #2", "48": "y x #2", "49": "y x #2", "50": "y x #2", "51": "y x #2", "52": "y x #2", "53": "y x #2", "54": "y x #2", "55": "y x #2", "56": "y x #2", "57": "y x #2", "58": "y x #2", "59": "y x #2", "60": "y x #2", "61": "y x #2", "62": "y x #2", "63": "y x #2", "64": "y x #2", "65": "y x #2", "66": "y x #2", "67": "y x #2", "68": "y x #2", "69": "y x #2"}, "safari": {"3.1": "u", "3.2": "u", "4": "u", "5": "u", "5.1": "y x #2", "6": "y x #2", "6.1": "y x #2", "7": "y x #2", "7.1": "y x #2", "8": "y x #2", "9": "y x #2", "9.1": "y x #2", "10": "y x #2", "10.1": "y x #2", "11": "y x #2", "11.1": "y x #2", "TP": "y x #2"}, "opera": {"9": "n", "9.5-9.6": "n", "10.0-10.1": "n", "10.5": "n", "10.6": "n", "11": "n", "11.1": "n", "11.5": "n", "11.6": "n", "12": "n", "12.1": "n", "15": "y x #2", "16": "y x #2", "17": "y x #2", "18": "y x #2", "19": "y x #2", "20": "y x #2", "21": "y x #2", "22": "y x #2", "23": "y x #2", "24": "y x #2", "25": "y x #2", "26": "y x #2", "27": "y x #2", "28": "y x #2", "29": "y x #2", "30": "y x #2", "31": "y x #2", "32": "y x #2", "33": "y x #2", "34": "y x #2", "35": "y x #2", "36": "y x #2", "37": "y x #2", "38": "y x #2", "39": "y x #2", "40": "y x #2", "41": "y x #2", "42": "y x #2", "43": "y x #2", "44": "y x #2", "45": "y x #2", "46": "y x #2", "47": "y x #2", "48": "y x #2", "49": "y x #2", "50": "y x #2", "51": "y x #2", "52": "y x #2"}, "ios_saf": {"3.2": "u", "4.0-4.1": "u", "4.2-4.3": "u", "5.0-5.1": "u", "6.0-6.1": "u", "7.0-7.1": "y x #2", "8": "y x #2", "8.1-8.4": "y x #2", "9.0-9.2": "y x #2", "9.3": "y x #2", "10.0-10.2": "y x #2", "10.3": "y x #2", "11.0-11.2": "y x #2", "11.3": "y x #2"}, "op_mini": {"all": "n"}, "android": {"2.1": "u", "2.2": "u", "2.3": "y x #2", "3": "y x #2", "4": "y x #2", "4.1": "y x #2", "4.2-4.3": "y x #2", "4.4": "y x #2", "4.4.3-4.4.4": "y x #2", "62": "y x #2"}, "bb": {"7": "y x #2", "10": "y x #2"}, "op_mob": {"10": "n", "11": "n", "11.1": "n", "11.5": "n", "12": "n", "12.1": "n", "37": "y x #2"}, "and_chr": {"66": "y x #2"}, "and_ff": {"57": "n"}, "ie_mob": {"10": "n", "11": "n"}, "and_uc": {"11.8": "y x #2"}, "samsung": {"4": "y x #2", "5": "y x #2", "6.2": "y x #2"}, "and_qq": {"1.2": "y x #2"}, "baidu": {"7.12": "y x #2"}}, "notes": "Currently scrollbar styling doesn't appear to be on any standards track.", "notes_by_num": {"1": "Only supports styling [scrollbar colors](https://msdn.microsoft.com/en-us/library/ms531155%28v=vs.85%29.aspx), no other properties to define the scrollbar's appearance.", "2": "Supports scrollbar styling via CSS [pseudo-properties](https://webkit.org/blog/363/styling-scrollbars/)."}, "usage_perc_y": 84.59, "usage_perc_a": 3.2, "ucprefix": false, "parent": "", "keywords": "scrollbar-button,scrollbar-track,scrollbar-thumb,scrollbar-base-color,scrollbar-face-color", "ie_id": "", "chrome_id": "", "firefox_id": "", "webkit_id": "", "shown": true}